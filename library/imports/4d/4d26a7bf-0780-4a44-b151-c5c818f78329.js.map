{"version": 3, "sources": ["assets/scripts/test/DebugShowMinesTest.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAEtF,8CAA6C;AAC7C,4DAA2D;AAC3D,6CAA4C;AAC5C,qDAAkD;AAE5C,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAgD,sCAAY;IAA5D;QAAA,qEAmEC;QAhEG,gBAAU,GAAc,IAAI,CAAC;QAG7B,iBAAW,GAAa,IAAI,CAAC;;IA6DjC,CAAC;IA3DG,kCAAK,GAAL;QACI,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC;SAC1E;QAED,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,wBAAwB,CAAC;SACtD;IACL,CAAC;IAED,wBAAwB;IACxB,sDAAyB,GAAzB;QAAA,iBAcC;QAbG,EAAE,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAE/B,yBAAyB;QACzB,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,qBAAqB,EAAE,EAAE,CAAC,CAAC;QAE5E,gBAAgB;QAChB,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,sBAAsB,EAAE,CAAC;QAClC,CAAC,EAAE,GAAG,CAAC,CAAC;QAER,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,qBAAqB,CAAC;SACnD;IACL,CAAC;IAED,UAAU;IACF,mDAAsB,GAA9B;QACI,cAAc;QACd,IAAM,iBAAiB,GAAG;YACtB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YACd,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YACd,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YACd,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YACd,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;SACjB,CAAC;QAEF,aAAa;QACb,IAAM,WAAW,GAAG;YAChB,KAAK,EAAE,qBAAS,CAAC,qBAAqB;YACtC,IAAI,EAAE,CAAC;YACP,GAAG,EAAE,SAAS;YACd,IAAI,EAAE,iBAAiB;SAC1B,CAAC;QAEF,SAAS;QACT,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE1D,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,+CAAU,iBAAiB,CAAC,MAAM,mCAAO,CAAC;SACvE;IACL,CAAC;IAED,sCAAS,GAAT;QACI,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC;SAC3E;IACL,CAAC;IA/DD;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;0DACS;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;2DACU;IANZ,kBAAkB;QADtC,OAAO;OACa,kBAAkB,CAmEtC;IAAD,yBAAC;CAnED,AAmEC,CAnE+C,EAAE,CAAC,SAAS,GAmE3D;kBAnEoB,kBAAkB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { MessageId } from \"../net/MessageId\";\nimport { WebSocketManager } from \"../net/WebSocketManager\";\nimport { GameMgr } from \"../common/GameMgr\";\nimport { EventType } from \"../common/EventCenter\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class DebugShowMinesTest extends cc.Component {\n\n    @property(cc.Button)\n    testButton: cc.Button = null;\n\n    @property(cc.Label)\n    statusLabel: cc.Label = null;\n\n    start() {\n        if (this.testButton) {\n            this.testButton.node.on('click', this.sendDebugShowMinesMessage, this);\n        }\n\n        if (this.statusLabel) {\n            this.statusLabel.string = '点击按钮测试DebugShowMines消息';\n        }\n    }\n\n    // 发送测试的DebugShowMines消息\n    sendDebugShowMinesMessage() {\n        cc.log(\"发送DebugShowMines测试消息\");\n        \n        // 发送DebugShowMines消息到服务器\n        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeDebugShowMines, {});\n\n        // 模拟服务器响应（用于测试）\n        this.scheduleOnce(() => {\n            this.simulateServerResponse();\n        }, 1.0);\n\n        if (this.statusLabel) {\n            this.statusLabel.string = '已发送DebugShowMines消息';\n        }\n    }\n\n    // 模拟服务器响应\n    private simulateServerResponse() {\n        // 创建模拟的地雷位置数据\n        const testMinePositions = [\n            { x: 1, y: 1 },\n            { x: 3, y: 2 },\n            { x: 5, y: 4 },\n            { x: 2, y: 6 },\n            { x: 7, y: 3 }\n        ];\n\n        // 模拟接收到的消息格式\n        const messageBean = {\n            msgId: MessageId.MsgTypeDebugShowMines,\n            code: 0,\n            msg: \"success\",\n            data: testMinePositions\n        };\n\n        // 发送消息事件\n        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);\n\n        if (this.statusLabel) {\n            this.statusLabel.string = `模拟响应：显示${testMinePositions.length}个地雷位置`;\n        }\n    }\n\n    onDestroy() {\n        if (this.testButton) {\n            this.testButton.node.off('click', this.sendDebugShowMinesMessage, this);\n        }\n    }\n}\n"]}