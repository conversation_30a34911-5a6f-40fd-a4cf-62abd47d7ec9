{"version": 3, "sources": ["assets/scripts/bean/GameBean.ts"], "names": [], "mappings": "", "file": "", "sourceRoot": "/", "sourcesContent": ["\n\n\nexport interface LoginData {\n    userInfo: UserInfo;\n    roomId: number; //这个房间号>0 的话 就是已经在游戏中断线重连的\n    inviteCode: number; //这个邀请码>0 的话 就是已经创建好房间 之后断线重连的\n    roomConfigs: RoomConfig[];\n}\n\nexport interface RoomConfig {\n    id: number;//房间类型 1-普通场 2-私人场\n    fees: number[];// 入场费列表\n    playerNums: number[];// 玩家人数列表\n}\n\nexport interface UserInfo {\n    userId: string;// 玩家ID\n    nickname: string;// 昵称\n    avatar: string; // 头像\n    coin: number;// 当前金币\n    serverTime: number;// 服务器的秒级时间戳\n\n}\n\n//请求匹配\nexport interface PairRequest {\n    playerNum: number;// 玩家人数\n    fee: number;// 房间费\n}\n\n//创建邀请的请求\nexport interface CreateInvite {\n    playerNum: number;// 玩家人数\n    fee: number;// 房间费\n}\n\nexport interface InviteInfo {\n    inviteCode: number;// 邀请码\n    playerNum: number;// 玩家人数\n    fee: number;// 房间费\n    propMode: number;// 0 无道具 1 有道具\n    creatorId: string;// 创建者用户ID\n    users: InviteUser[];// 匹配到的所有玩家\n}\n\nexport interface InviteUser {\n    userId: string;// 玩家Id\n    nickname: string;// 昵称\n    avatar: string;// 头像\n    ready: boolean;// 是否准备好\n    creator: boolean;// 是否邀请的发起者\n    onLine: boolean;// 是否在线\n}\n\n\n//接受邀请的请求\nexport interface AcceptInvite {\n    inviteCode: number;// 邀请码\n}\n\n// AcceptInvite 接受邀请\nexport interface AcceptInvite {\n    userId: string;// 用户ID\n    inviteInfo: InviteInfo;// 邀请信息\n}\n\n// InviteReady 邀请者改变准备状态\nexport interface InviteReady {\n    ready: boolean;// true 准备 false 取消准备\n}\n\n// NoticeUserInviteStatus 广播玩家的邀请状态˝\nexport interface NoticeUserInviteStatus {\n    userId: string;// 玩家Id\n    ready: boolean;// 是否准备好\n    onLine: boolean;// 是否在线\n}\n\n// ChangeInviteCfg 更改邀请配置\nexport interface ChangeInviteCfgRequest {\n    inviteCode: number;// 邀请码\n    fee: number;// 房间费\n}\n\nexport interface ChangeInviteCfgResult {\n    fee: number;// 房间费\n}\n\n// NoticeLeaveInvite 邀请广播有人离开\nexport interface NoticeLeaveInvite {\n    userId: string;// 玩家Id\n    isCreator: boolean;// 离开者是否是创建者\n}\n\n//创建者踢出玩家请求\nexport interface InviteKickOut {\n    userId: string // 被踢除的玩家Id\n}\n\n//通知邀请状态\nexport interface NoticeUserInviteStatus {\n    userId: string  //用户 id\n    ready: boolean //准备状态\n    onLine: boolean //是否在线\n}\n\n\n//////////////////////////////////////////游戏内的数据//////////////////////////////////////////\n\n//开始游戏和 重连都走这一个\nexport interface NoticeStartGame {\n\n    roomId: number;// 房间ID\n    roomType: number; // 房间类型  EnumBean.RoomType\n    playerNum: number;// 玩家人数\n    mapType: number;// 地图类型 0-方形地图，1-六边形地图\n    fee: number; // 房间费\n    propMode: number;// 道具模式\n    specialFull: number;// 多少特殊块填满技能槽\n    specialRemoved: number;// 已移除特殊块数量\n    users: RoomUser[];// 匹配到的所有玩家\n    gameStatus: number;// 游戏状态  EnumBean.GameStatus\n    countDown: number;// 游戏状态倒计时\n    blockList: Block[];// 地图\n    opIndex: number;// 操作索引\n    lookPos: number;// 旁观座位号\n    curTask: CurTask// 当前任务\n    validHexCoords?: HexCoord[];// 有效的六边形坐标列表（仅mapType=1时返回）\n    mapConfig?: MapConfig;// 地图配置信息（仅mapType=0时返回）\n\n}\n\nexport interface RoomUser {\n    userId: string;// 用户ID\n    nickName: string;// 昵称\n    avatar: string;// 头像\n    pos: number;// 座位号\n    coin: number;// 玩家最新金币\n    status: number;// 玩家状态   EnumBean.UserStatus\n    score: number;// 消除分数\n    rank: number //当前排名\n}\n\n// 地图配置信息（方形地图专用）\nexport interface MapConfig {\n    width: number;// 地图宽度\n    height: number;// 地图高度\n    mineCount: number;// 地雷总数\n}\n\n// 六边形坐标结构（六边形地图专用）\nexport interface HexCoord {\n    q: number;// 六边形坐标系q坐标\n    r: number;// 六边形坐标系r坐标\n}\n\nexport interface Block {\n    id: number; // 块的ID\n    color: number; // 块颜色 EnumBean.BlockColor  1-红色 2-蓝色 3-绿色 4-黄色 5-紫色\n    type: number; // 块类型 EnumBean.BlockType 1-普通块 2-箭头X 3-箭头Y 4-炸弹 5-彩虹 6-超级箭头 7-炸弹箭头 8-彩虹箭头 9-超级炸弹 10-彩虹炸弹 11-超级彩虹\n    obstacle: Obstacle; // 障碍物  \n}\n\nexport interface Obstacle {\n    type: number; //EnumBean.Obstacle\n    fromUid: string //如果 type==1002 的话会有两个用户 id 用，隔开，前面的id是发射锁链的用户后面的id是发射冰块的用户\n\n}\n// ObstacleBlock 障碍物的块\nexport interface ObstacleBlock {\n    id: number; // 旧的块ID(未掉落前的位置)\n    obstacle: Obstacle // 障碍物 （这里代表消除障碍物之后 剩下的状态）\n}\n\n\nexport interface CurTask {\n    id: number;// 任务ID\n    color: number;// 目标颜色\n    require: number;// 需要完成的数量\n    current: number;// 当前进度\n    reward: number;// 奖励(1-彩虹、2-冰块、3-锁链、4-箭头X、5-箭头Y)\n}\n\n\n//移动块-MoveBlock 的请求数据   \nexport interface MoveBlock {\n    id: number;// 块ID\n    otherId: number;// 另一个块ID\n}\n\n// NoticeScoreChg 通知分数变化(消息ID-ScoreChg)\nexport interface NoticeScoreChg {\n    userId: string;// 玩家Id\n    score: number;// 新的分数\n    taskObstacle: ObstacleBlock // 障碍物\n\n}\n// MoveBlockFail 移动块失败\nexport interface MoveBlockFail {\n    id: number;// 块ID\n    otherId: number;// 另一个块ID\n}\n\n// NoticeMoveBlock 通知移动块\nexport interface NoticeMoveBlock {\n    userId: string;// 玩家Id\n    id: number;// 块ID\n    otherId: number;// 另一个块ID\n    score: number;// 新的分数\n    specialRemoved: number;// 新的已移除特殊块数量\n    groups: MoveGroup[];// 匹配分组的列表\n    activate: Activate;// 激活特殊道具\n    opIndex: number;// 操作索引\n    isTaskOver: boolean// // 是否旧任务结束\n    taskReward: TaskReward// // 任务奖励\n    curTask: CurTask// 当前任务\n    obstacles:ObstacleBlock[] // 障碍物 (在这里没啥用  就是用来打印数据的)\n}\n\n// TaskReward 任务奖励\nexport interface TaskReward {\n    id: number;// 块ID\n    type: number;// 块类型 EnumBean.BlockType\n\n}\n\n// NoticeActivate 通知激活特殊道具\nexport interface Activate {\n    times: number  // 激活次数\n    rectIDs:number[] //激活区域四个块 中左上角的块\n    groups: MoveGroup[] // 匹配分组的列表\n}\n\n// MoveGroup 匹配分组\nexport interface MoveGroup {\n    matches: Match[];// 匹配列表\n    drops: DropBlock[];// 新掉落的块列表\n    scoreChg: number;// 分数变化\n    obstaclesChg:ObstacleBlock[] //\n}\n\nexport interface DropBlock {\n    id: number; // 块的ID\n    color: number; // 块颜色 EnumBean.BlockColor  1-红色 2-蓝色 3-绿色 4-黄色 5-紫色\n}\n\nexport interface UserSettlement {\n    userId: string;// 玩家Id\n    coinChg: number;// 金币变化\n    coin: number;// 玩家最新金币\n    rank: number;// 排名\n    score: number;// 分数\n}\n\n// Match 单个匹配\nexport interface Match {\n    removes: Remove[];// 移动后可消除的块列表(也可能两个特殊块叠一起，不会构成>=3的同颜色消除)\n    merge: MergeBean;// 合成块\n    passives: Passive[];// 被动消除列表(Removes中有N个特殊块)\n}\n\nexport interface MergeBean{\n    id: number;// 块ID\n    type:null;// 块类型 EnumBean.BlockType\n}\n\n// Passive 被动消除\nexport interface Passive {\n    id: number;// 块ID\n    type: number;// 块类型 EnumBean.BlockType\n    score: number;// 得分\n    obstacle: number;// 障碍物\n    removes: Remove[];// 消除的块列表\n    passives: Passive[];// 被动消除列表(Removes中有N个特殊块)\n}\n\n// PassiveRemove 被动消除移除的块\nexport interface Remove {\n    id: number;// 块ID\n    score: number;// 得分\n}\n\nexport interface NoticeSettlement {\n    users?: UserSettlement[];// 闲家结算列表（旧版本兼容）\n    finalRanking?: PlayerFinalResult[];// 最终排名列表（扫雷游戏）\n    fee?: number;// 房间费用\n    gameStats?: GameStats;// 游戏统计信息\n    gameType?: string;// 游戏类型\n    playerCount?: number;// 玩家数量\n    totalRounds?: number;// 总回合数\n}\n\nexport interface UserSettlement {\n    userId: string;// 玩家Id\n    coinChg: number;// 金币变化\n    coin: number;// 玩家最新金币\n    rank: number;// 排名\n    score: number;// 分数\n}\n\n// 游戏统计信息\nexport interface GameStats {\n    mapSize: string;// 地图大小\n    mineCount: number;// 地雷数量\n    revealedCount: number;// 已揭示方块数量\n}\n\n//玩家主动离开房间的请求数据\nexport interface LeaveRoom {\n    isConfirmLeave: boolean;// 确认离开房间\n}\n\n//玩家主动离开房间-LeaveRoom\nexport interface NoticeLeaveRoom {\n    userId: string;// 玩家Id\n}\n\n//玩家被踢出房间-KickOutUser\nexport interface NoticeUserKickOut {\n    userId: string;// 玩家Id\n}\n\nexport interface IllegalOperation {\n    id: number; //移动块\n    otherId: number; //被交换块\n    tokenUserId: string; //当前操作用户的id，不是此次操作用户的 id\n}\n\n// 扫雷游戏相关接口定义\n\n// 扫雷回合开始通知\nexport interface NoticeRoundStart {\n    roundNumber: number; // 回合编号（从1开始）\n    countDown: number; // 回合倒计时（25秒）\n    gameStatus: number; // 游戏状态（0-扫雷进行中）\n}\n\n// 扫雷操作展示通知\nexport interface NoticeActionDisplay {\n    roundNumber: number; // 当前回合编号\n    gameStatus: number; // 游戏状态（0-扫雷进行中）\n    countDown: number; // 剩余倒计时（5秒展示阶段）\n    playerActions: PlayerActionDisplay[]; // 玩家操作展示列表\n    floodFillResults?: FloodFillResult[]; // 连锁展开结果列表（可选）\n    playerTotalScores: {[userId: string]: number}; // 玩家累计总得分对象\n    remainingMines: number; // 剩余炸弹数量\n    message: string; // 提示信息\n}\n\n// 玩家操作展示结构\nexport interface PlayerActionDisplay {\n    userId: string; // 玩家ID\n    x: number; // 操作坐标x\n    y: number; // 操作坐标y\n    action: number; // 操作类型（1-挖掘，2-标记）\n    score: number; // 本次操作得分\n    isFirstChoice: boolean; // 是否为首选玩家\n    result: number | string; // 操作结果（挖掘：数字或\"mine\"；标记：\"correct_mark\"或\"wrong_mark\"）\n}\n\n// 扫雷回合结束通知\nexport interface NoticeRoundEnd {\n    roundNumber: number; // 当前回合编号\n    gameStatus: number; // 游戏状态（1-回合结束展示）\n    countDown: number; // 回合结束展示倒计时（5秒）\n    playerResults: PlayerRoundResult[]; // 玩家操作结果列表\n    mapData: MineBlock[][]; // 地图数据（只显示已揭示的方块）\n    floodFillResults?: FloodFillResult[]; // 连锁展开结果列表（可选）\n}\n\n// 玩家回合结果结构\nexport interface PlayerRoundResult {\n    userId: string; // 玩家ID\n    x: number; // 操作坐标x\n    y: number; // 操作坐标y\n    action: number; // 操作类型（1-挖掘，2-标记）\n    score: number; // 本回合得分\n    isFirstChoice: boolean; // 是否为首选玩家（享受+1分奖励）\n    isMine: boolean; // 操作的方块是否是地雷\n    neighborMines: number; // 操作方块周围的地雷数量\n}\n\n// 连锁展开结果结构\nexport interface FloodFillResult {\n    revealedBlocks: RevealedBlock[]; // 连锁揭示的方块列表\n    totalRevealed: number; // 总共揭示的方块数\n    triggerUserId: string; // 触发连锁展开的玩家ID\n    triggerX: number; // 触发点X坐标\n    triggerY: number; // 触发点Y坐标\n}\n\n// 揭示方块结构\nexport interface RevealedBlock {\n    x: number; // 方块X坐标\n    y: number; // 方块Y坐标\n    neighborMines: number; // 周围地雷数量\n    isMine: boolean; // 是否是地雷\n    triggerUserId: string; // 触发揭示的玩家ID\n}\n\n// 扫雷首选玩家奖励通知\nexport interface NoticeFirstChoiceBonus {\n    userId: string; // 玩家ID\n    roundNumber: number; // 回合编号\n    bonusScore: number; // 首选玩家奖励分数（固定+1）\n    totalScore: number; // 累计总得分（包含此奖励）\n}\n\n// AI托管状态变更通知\nexport interface AIStatusChange {\n    userId: string; // 状态变更的用户ID\n    isAIManaged: boolean; // 是否进入AI托管（true=进入，false=退出）\n    timestamp: number; // 状态变更时间戳\n}\n\n// 扫雷游戏结束通知\nexport interface NoticeGameEnd {\n    gameStatus: number; // 游戏状态（2-游戏结束）\n    countDown: number; // 游戏结束展示倒计时（10秒）\n    finalRanking: PlayerFinalResult[]; // 最终排名列表\n    completeMapData: MineBlock[][]; // 完整地图数据（显示所有地雷和信息）\n    totalRounds: number; // 总回合数\n}\n\n// 玩家最终结果结构\nexport interface PlayerFinalResult {\n    userId: string; // 玩家ID\n    totalScore: number; // 总得分\n    rank: number; // 最终排名\n    coinChg: number; // 金币变化\n    mineHits: number; // 踩雷次数\n}\n\n// 扫雷方块结构（已在API.md中定义，这里重新定义以确保类型一致）\nexport interface MineBlock {\n    x: number; // x坐标（0-7）\n    y: number; // y坐标（0-7）\n    isRevealed: boolean; // 是否已揭开\n    isMarked: boolean; // 是否被标记为地雷\n    players: string[]; // 当前格子中的玩家ID列表\n    NeighborMines: number; // 周围地雷数量(0-8)\n    IsMine: boolean; // 是否是地雷\n}\n\n// 点击方块请求（方形地图）\nexport interface ClickBlockRequest {\n    x: number; // 方块x坐标（0-7，从左到右）\n    y: number; // 方块y坐标（0-7，从下到上，左下角为(0,0)）\n    action: number; // 操作类型：1=挖掘方块，2=标记/取消标记地雷\n}\n\n// 点击六边形方块请求（六边形地图）\nexport interface ClickHexBlockRequest {\n    q: number; // 六边形坐标系q坐标\n    r: number; // 六边形坐标系r坐标\n    action: number; // 操作类型：1=挖掘方块，2=标记/取消标记地雷\n}\n\n// 关卡信息请求\nexport interface ExtendLevelInfoRequest {\n    levelId: number; // 关卡编号\n}\n\n// 关卡信息响应\nexport interface ExtendLevelInfoResponse {\n    levelId: number; // 关卡编号（后端实际返回的字段名）\n    levelNumber?: number; // 关卡编号（兼容旧版本）\n    mapType: number; // 地图类型 0-方形地图，1-六边形地图\n    mapConfig?: MapConfig; // 地图配置信息（方形地图）\n    validHexCoords?: HexCoord[]; // 有效的六边形坐标列表（六边形地图）\n    mineCount: number; // 地雷总数\n    mapSize?: string; // 地图大小描述\n    mapWidth: number; // 地图宽度\n    mapHeight: number; // 地图高度\n    isCleared: boolean; // 是否已通关\n    isSpecial: boolean; // 是否特殊关卡\n    isUnlocked: boolean; // 是否已解锁\n    roomId?: number; // 关卡游戏房间ID（用于退出游戏）\n    gameStatus?: number; // 游戏状态\n    countDown?: number; // 倒计时\n}\n"]}