{"version": 3, "sources": ["assets/scripts/test/NoticeRoundStartTest.ts"], "names": [], "mappings": ";;;;;AAAA,4BAA4B;AAC5B,6CAA6C;;;;;;;;;;;;;;;;;;;;;AAE7C,6CAA4C;AAC5C,qDAAkD;AAClD,8CAA6C;AAE7C,iDAAgD;AAE1C,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAkD,wCAAY;IAA9D;QAAA,qEA4ZC;QAzZG,gBAAU,GAAc,IAAI,CAAC;QAG7B,2BAAqB,GAAc,IAAI,CAAC;QAGxC,+BAAyB,GAAc,IAAI,CAAC;QAG5C,gCAA0B,GAAc,IAAI,CAAC;QAG7C,2BAAqB,GAAc,IAAI,CAAC;QAGxC,iBAAW,GAAa,IAAI,CAAC;;IA0YjC,CAAC;IAxYG,oCAAK,GAAL;QACI,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;SAChE;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;SACpF;QAED,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;SACtF;QAED,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;SACxF;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;SAC9E;QAED,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC;SACxC;IACL,CAAC;IAED,0BAA0B;IAC1B,8CAAe,GAAf;QAGI,SAAS;QACT,IAAM,QAAQ,GAAqB;YAC/B,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,EAAE;YACb,UAAU,EAAE,CAAC;SAChB,CAAC;QAEF,aAAa;QACb,IAAM,WAAW,GAAG;YAChB,KAAK,EAAE,qBAAS,CAAC,uBAAuB;YACxC,IAAI,EAAE,CAAC;YACP,GAAG,EAAE,SAAS;YACd,IAAI,EAAE,QAAQ;SACjB,CAAC;QAEF,SAAS;QACT,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE1D,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,6DAAc,QAAQ,CAAC,WAAW,4BAAQ,QAAQ,CAAC,SAAS,WAAG,CAAC;SAC7F;IACL,CAAC;IAED,YAAY;IACZ,kDAAmB,GAAnB,UAAoB,OAAe;QAC/B,IAAM,QAAQ,GAAqB;YAC/B,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,OAAO;YAClB,UAAU,EAAE,CAAC;SAChB,CAAC;QAEF,IAAM,WAAW,GAAG;YAChB,KAAK,EAAE,qBAAS,CAAC,uBAAuB;YACxC,IAAI,EAAE,CAAC;YACP,GAAG,EAAE,SAAS;YACd,IAAI,EAAE,QAAQ;SACjB,CAAC;QAEF,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE1D,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,qCAAU,OAAO,WAAG,CAAC;SAClD;IACL,CAAC;IAED,YAAY;IACZ,sDAAuB,GAAvB;QAAA,iBAoBC;QAnBG,WAAW;QACX,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;QACjC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,mBAAmB;QACnB,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;QACjC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,iBAAiB;QACjB,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,gBAAgB;QAChB,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC;IAED,4BAA4B;IAC5B,uDAAwB,GAAxB;QAGI,IAAM,QAAQ,GAAwB;YAClC,WAAW,EAAE,CAAC;YACd,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,CAAC;YACZ,aAAa,EAAE;gBACX;oBACI,MAAM,EAAE,YAAY;oBACpB,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,CAAC;oBACJ,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,aAAa,EAAE,IAAI;oBACnB,MAAM,EAAE,CAAC,CAAC,MAAM;iBACnB;gBACD;oBACI,MAAM,EAAE,YAAY;oBACpB,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,CAAC;oBACJ,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,aAAa,EAAE,KAAK;oBACpB,MAAM,EAAE,cAAc;iBACzB;aACJ;YACD,iBAAiB,EAAE;gBACf,YAAY,EAAE,CAAC;gBACf,YAAY,EAAE,CAAC;aAClB;YACD,cAAc,EAAE,EAAE;YAClB,OAAO,EAAE,eAAe;SAC3B,CAAC;QAEF,IAAM,WAAW,GAAG;YAChB,KAAK,EAAE,qBAAS,CAAC,0BAA0B;YAC3C,IAAI,EAAE,CAAC;YACP,GAAG,EAAE,SAAS;YACd,IAAI,EAAE,QAAQ;SACjB,CAAC;QAEF,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE1D,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,4FAA8B,QAAQ,CAAC,SAAS,4CAAS,QAAQ,CAAC,cAAc,WAAG,CAAC;SACjH;IACL,CAAC;IAED,YAAY;IACZ,gDAAiB,GAAjB;QAAA,iBAYC;QAXG,YAAY;QACZ,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,gBAAgB;QAChB,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,wBAAwB,EAAE,CAAC;QACpC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,eAAe,CAAC;SAC7C;IACL,CAAC;IAED,gBAAgB;IAChB,uDAAwB,GAAxB;QAAA,iBAmBC;QAhBG,YAAY;QACZ,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,eAAe;QACf,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,2BAA2B,EAAE,CAAC;QACvC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,uBAAuB;QACvB,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,uCAAuC,EAAE,CAAC;QACnD,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,iBAAiB,CAAC;SAC/C;IACL,CAAC;IAED,+BAA+B;IAC/B,0DAA2B,GAA3B;QAGI,IAAM,QAAQ,GAA2B;YACrC,MAAM,EAAE,YAAY;YACpB,WAAW,EAAE,CAAC;YACd,UAAU,EAAE,CAAC;YACb,UAAU,EAAE,CAAC,CAAC,gBAAgB;SACjC,CAAC;QAEF,IAAM,WAAW,GAAG;YAChB,KAAK,EAAE,qBAAS,CAAC,6BAA6B;YAC9C,IAAI,EAAE,CAAC;YACP,GAAG,EAAE,SAAS;YACd,IAAI,EAAE,QAAQ;SACjB,CAAC;QAEF,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE1D,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,sFAAyC,CAAC;SACvE;IACL,CAAC;IAED,mCAAmC;IACnC,sEAAuC,GAAvC;QAGI,IAAM,QAAQ,GAAwB;YAClC,WAAW,EAAE,CAAC;YACd,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,CAAC;YACZ,aAAa,EAAE;gBACX;oBACI,MAAM,EAAE,YAAY;oBACpB,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,CAAC;oBACJ,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,aAAa,EAAE,IAAI;oBACnB,MAAM,EAAE,CAAC,CAAC,MAAM;iBACnB;gBACD;oBACI,MAAM,EAAE,YAAY;oBACpB,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,CAAC;oBACJ,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,aAAa,EAAE,KAAK;oBACpB,MAAM,EAAE,cAAc;iBACzB;aACJ;YACD,iBAAiB,EAAE;gBACf,YAAY,EAAE,CAAC;gBACf,YAAY,EAAE,CAAC,CAAE,eAAe;aACnC;YACD,cAAc,EAAE,CAAC;YACjB,OAAO,EAAE,oCAAoC;SAChD,CAAC;QAEF,IAAM,WAAW,GAAG;YAChB,KAAK,EAAE,qBAAS,CAAC,0BAA0B;YAC3C,IAAI,EAAE,CAAC;YACP,GAAG,EAAE,SAAS;YACd,IAAI,EAAE,QAAQ;SACjB,CAAC;QAEF,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE1D,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,qFAA8B,CAAC;SAC5D;IACL,CAAC;IAED;;OAEG;IACH,qDAAsB,GAAtB;QAAA,iBAkCC;QAjCG,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,aAAa,CAAC;SAC3C;QAED,yBAAyB;QACzB,IAAM,kBAAkB,GAAI,MAAc,CAAC,kBAAkB,CAAC;QAC9D,IAAI,kBAAkB,EAAE;YACpB,WAAW;YACX,IAAI,kBAAkB,CAAC,sBAAsB,EAAE;gBAC3C,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;gBAC5C,IAAI,IAAI,CAAC,WAAW,EAAE;oBAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC;iBACzC;gBAED,QAAQ;gBACR,IAAI,CAAC,YAAY,CAAC;oBACd,IAAI,kBAAkB,CAAC,sBAAsB,EAAE;wBAC3C,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;wBAC5C,IAAI,KAAI,CAAC,WAAW,EAAE;4BAClB,KAAI,CAAC,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC;yBACzC;qBACJ;gBACL,CAAC,EAAE,CAAC,CAAC,CAAC;aACT;iBAAM;gBACH,IAAI,IAAI,CAAC,WAAW,EAAE;oBAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,iDAAiD,CAAC;iBAC/E;aACJ;SACJ;aAAM;YACH,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,yBAAyB,CAAC;aACvD;SACJ;IACL,CAAC;IAED;;OAEG;IACH,sDAAuB,GAAvB;QACI,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,aAAa,CAAC;SAC3C;QAED,yBAAyB;QACzB,IAAM,kBAAkB,GAAI,MAAc,CAAC,kBAAkB,CAAC;QAC9D,IAAI,kBAAkB,EAAE;YACpB,WAAW;YACX,IAAI,kBAAkB,CAAC,uBAAuB,EAAE;gBAC5C,kBAAkB,CAAC,uBAAuB,EAAE,CAAC;gBAC7C,IAAI,IAAI,CAAC,WAAW,EAAE;oBAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC;iBACzC;aACJ;iBAAM;gBACH,IAAI,IAAI,CAAC,WAAW,EAAE;oBAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,kDAAkD,CAAC;iBAChF;aACJ;SACJ;aAAM;YACH,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,yBAAyB,CAAC;aACvD;SACJ;IACL,CAAC;IAED,eAAe;IACf,iDAAkB,GAAlB;QAAA,iBA4CC;;QA3CG,gBAAgB;QAChB,IAAM,aAAa,GAAG,aAAA,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,0CAAE,QAAQ,0CAAE,MAAM,KAAI,YAAY,CAAC;QAE3F,IAAM,QAAQ,GAAmB;YAC7B,MAAM,EAAE,aAAa;YACrB,WAAW,EAAE,IAAI;YACjB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;QAEF,IAAM,WAAW,GAAG;YAChB,KAAK,EAAE,qBAAS,CAAC,qBAAqB;YACtC,IAAI,EAAE,CAAC;YACP,GAAG,EAAE,SAAS;YACd,IAAI,EAAE,QAAQ;SACjB,CAAC;QAEF,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE1D,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,+DAAgB,aAAa,6EAAc,CAAC;SACzE;QAED,4BAA4B;QAC5B,IAAI,CAAC,YAAY,CAAC;YACd,IAAM,QAAQ,GAAmB;gBAC7B,MAAM,EAAE,aAAa;gBACrB,WAAW,EAAE,KAAK;gBAClB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB,CAAC;YAEF,IAAM,eAAe,GAAG;gBACpB,KAAK,EAAE,qBAAS,CAAC,qBAAqB;gBACtC,IAAI,EAAE,CAAC;gBACP,GAAG,EAAE,SAAS;gBACd,IAAI,EAAE,QAAQ;aACjB,CAAC;YAEF,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;YAE9D,IAAI,KAAI,CAAC,WAAW,EAAE;gBAClB,KAAI,CAAC,WAAW,CAAC,MAAM,GAAG,+DAAgB,aAAa,6EAAc,CAAC;aACzE;QACL,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED,wCAAS,GAAT;QACI,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;SACjE;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;SACrF;QACD,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;SACvF;QACD,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;SACzF;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;SAC/E;IACL,CAAC;IAxZD;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACS;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;uEACoB;IAGxC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;2EACwB;IAG5C;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4EACyB;IAG7C;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;uEACoB;IAGxC;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;6DACU;IAlBZ,oBAAoB;QADxC,OAAO;OACa,oBAAoB,CA4ZxC;IAAD,2BAAC;CA5ZD,AA4ZC,CA5ZiD,EAAE,CAAC,SAAS,GA4Z7D;kBA5ZoB,oBAAoB", "file": "", "sourceRoot": "/", "sourcesContent": ["// 测试NoticeRoundStart消息处理的脚本\n// 这个脚本可以用来模拟发送NoticeRoundStart消息，测试前端计时器更新功能\n\nimport { GameMgr } from \"../common/GameMgr\";\nimport { EventType } from \"../common/EventCenter\";\nimport { MessageId } from \"../net/MessageId\";\nimport { NoticeRoundStart, NoticeActionDisplay, NoticeFirstChoiceBonus, AIStatusChange } from \"../bean/GameBean\";\nimport { GlobalBean } from \"../bean/GlobalBean\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class NoticeRoundStartTest extends cc.Component {\n\n    @property(cc.Button)\n    testButton: cc.Button = null;\n\n    @property(cc.Button)\n    firstChoiceTestButton: cc.Button = null;\n\n    @property(cc.Button)\n    testGameStartAnimationBtn: cc.Button = null;\n\n    @property(cc.Button)\n    testRoundStartAnimationBtn: cc.Button = null;\n\n    @property(cc.Button)\n    testAIStatusChangeBtn: cc.Button = null;\n\n    @property(cc.Label)\n    statusLabel: cc.Label = null;\n\n    start() {\n        if (this.testButton) {\n            this.testButton.node.on('click', this.sendTestMessage, this);\n        }\n\n        if (this.firstChoiceTestButton) {\n            this.firstChoiceTestButton.node.on('click', this.testFirstChoiceBonusFlow, this);\n        }\n\n        if (this.testGameStartAnimationBtn) {\n            this.testGameStartAnimationBtn.node.on('click', this.testGameStartAnimation, this);\n        }\n\n        if (this.testRoundStartAnimationBtn) {\n            this.testRoundStartAnimationBtn.node.on('click', this.testRoundStartAnimation, this);\n        }\n\n        if (this.testAIStatusChangeBtn) {\n            this.testAIStatusChangeBtn.node.on('click', this.testAIStatusChange, this);\n        }\n\n        if (this.statusLabel) {\n            this.statusLabel.string = '点击按钮测试消息';\n        }\n    }\n\n    // 发送测试的NoticeRoundStart消息\n    sendTestMessage() {\n       \n        \n        // 创建测试数据\n        const testData: NoticeRoundStart = {\n            roundNumber: 1,\n            countDown: 25,\n            gameStatus: 0\n        };\n\n        // 模拟接收到的消息格式\n        const messageBean = {\n            msgId: MessageId.MsgTypeNoticeRoundStart,\n            code: 0,\n            msg: \"success\",\n            data: testData\n        };\n\n        // 发送消息事件\n        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);\n        \n        if (this.statusLabel) {\n            this.statusLabel.string = `已发送测试消息: 回合${testData.roundNumber}, 倒计时${testData.countDown}秒`;\n        }\n    }\n\n    // 发送倒计时更新测试\n    sendCountdownUpdate(seconds: number) {\n        const testData: NoticeRoundStart = {\n            roundNumber: 1,\n            countDown: seconds,\n            gameStatus: 0\n        };\n\n        const messageBean = {\n            msgId: MessageId.MsgTypeNoticeRoundStart,\n            code: 0,\n            msg: \"success\",\n            data: testData\n        };\n\n        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);\n        \n        if (this.statusLabel) {\n            this.statusLabel.string = `倒计时更新: ${seconds}秒`;\n        }\n    }\n\n    // 测试不同的倒计时值\n    testDifferentCountdowns() {\n        // 测试25秒倒计时\n        this.scheduleOnce(() => {\n            this.sendCountdownUpdate(25);\n        }, 1);\n\n        // 测试20秒倒计时（进入展示阶段）\n        this.scheduleOnce(() => {\n            this.sendCountdownUpdate(20);\n        }, 3);\n\n        // 测试5秒倒计时（回合结束前）\n        this.scheduleOnce(() => {\n            this.sendCountdownUpdate(5);\n        }, 5);\n\n        // 测试0秒倒计时（回合结束）\n        this.scheduleOnce(() => {\n            this.sendCountdownUpdate(0);\n        }, 7);\n    }\n\n    // 发送NoticeActionDisplay测试消息\n    sendActionDisplayMessage() {\n     \n\n        const testData: NoticeActionDisplay = {\n            roundNumber: 1,\n            gameStatus: 0,\n            countDown: 5,\n            playerActions: [\n                {\n                    userId: \"player_001\",\n                    x: 3,\n                    y: 2,\n                    action: 1, // 挖掘\n                    score: 1,\n                    isFirstChoice: true,\n                    result: 2 // 数字2\n                },\n                {\n                    userId: \"player_002\",\n                    x: 1,\n                    y: 4,\n                    action: 2, // 标记\n                    score: 1,\n                    isFirstChoice: false,\n                    result: \"correct_mark\"\n                }\n            ],\n            playerTotalScores: {\n                \"player_001\": 5,\n                \"player_002\": 3\n            },\n            remainingMines: 10, // 剩余炸弹数量\n            message: \"展示阶段：显示所有玩家操作\"\n        };\n\n        const messageBean = {\n            msgId: MessageId.MsgTypeNoticeActionDisplay,\n            code: 0,\n            msg: \"success\",\n            data: testData\n        };\n\n        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);\n\n        if (this.statusLabel) {\n            this.statusLabel.string = `已发送ActionDisplay消息: 展示阶段，剩余${testData.countDown}秒，剩余炸弹${testData.remainingMines}个`;\n        }\n    }\n\n    // 测试完整的回合流程\n    testFullRoundFlow() {\n        // 1. 发送回合开始\n        this.sendTestMessage();\n\n        // 2. 20秒后发送操作展示\n        this.scheduleOnce(() => {\n            this.sendActionDisplayMessage();\n        }, 2);\n\n        if (this.statusLabel) {\n            this.statusLabel.string = '开始测试完整回合流程...';\n        }\n    }\n\n    // 测试先手奖励和后续加分流程\n    testFirstChoiceBonusFlow() {\n        \n\n        // 1. 发送回合开始\n        this.sendTestMessage();\n\n        // 2. 2秒后发送先手奖励\n        this.scheduleOnce(() => {\n            this.sendFirstChoiceBonusMessage();\n        }, 2);\n\n        // 3. 4秒后发送操作展示（包含先手玩家）\n        this.scheduleOnce(() => {\n            this.sendActionDisplayWithFirstChoiceMessage();\n        }, 4);\n\n        if (this.statusLabel) {\n            this.statusLabel.string = '测试先手奖励+本回合加分...';\n        }\n    }\n\n    // 发送NoticeFirstChoiceBonus测试消息\n    sendFirstChoiceBonusMessage() {\n    \n\n        const testData: NoticeFirstChoiceBonus = {\n            userId: \"player_001\",\n            roundNumber: 1,\n            bonusScore: 1,\n            totalScore: 6 // 原来5分 + 1分先手奖励\n        };\n\n        const messageBean = {\n            msgId: MessageId.MsgTypeNoticeFirstChoiceBonus,\n            code: 0,\n            msg: \"success\",\n            data: testData\n        };\n\n        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);\n\n        if (this.statusLabel) {\n            this.statusLabel.string = `已发送FirstChoiceBonus: player_001获得+1先手奖励`;\n        }\n    }\n\n    // 发送包含先手玩家的NoticeActionDisplay测试消息\n    sendActionDisplayWithFirstChoiceMessage() {\n      \n\n        const testData: NoticeActionDisplay = {\n            roundNumber: 1,\n            gameStatus: 0,\n            countDown: 5,\n            playerActions: [\n                {\n                    userId: \"player_001\",\n                    x: 3,\n                    y: 2,\n                    action: 1, // 挖掘\n                    score: 2, // 本回合得分\n                    isFirstChoice: true, // 先手玩家\n                    result: 3 // 数字3\n                },\n                {\n                    userId: \"player_002\",\n                    x: 1,\n                    y: 4,\n                    action: 2, // 标记\n                    score: 1,\n                    isFirstChoice: false,\n                    result: \"correct_mark\"\n                }\n            ],\n            playerTotalScores: {\n                \"player_001\": 8, // 原来6分 + 本回合2分\n                \"player_002\": 4  // 原来3分 + 本回合1分\n            },\n            remainingMines: 9, // 剩余炸弹数量\n            message: \"展示阶段：先手玩家应该显示两次player_game_pfb分数变化\"\n        };\n\n        const messageBean = {\n            msgId: MessageId.MsgTypeNoticeActionDisplay,\n            code: 0,\n            msg: \"success\",\n            data: testData\n        };\n\n        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);\n\n        if (this.statusLabel) {\n            this.statusLabel.string = `已发送ActionDisplay: 先手玩家应显示+2分`;\n        }\n    }\n\n    /**\n     * 测试游戏开始动画\n     */\n    testGameStartAnimation() {\n        if (this.statusLabel) {\n            this.statusLabel.string = \"测试游戏开始动画...\";\n        }\n\n        // 获取GamePageController实例\n        const gamePageController = (window as any).gamePageController;\n        if (gamePageController) {\n            // 调用游戏开始动画\n            if (gamePageController.showGameStartAnimation) {\n                gamePageController.showGameStartAnimation();\n                if (this.statusLabel) {\n                    this.statusLabel.string = \"游戏开始动画已触发\";\n                }\n\n                // 3秒后隐藏\n                this.scheduleOnce(() => {\n                    if (gamePageController.hideGameStartAnimation) {\n                        gamePageController.hideGameStartAnimation();\n                        if (this.statusLabel) {\n                            this.statusLabel.string = \"游戏开始动画已隐藏\";\n                        }\n                    }\n                }, 3);\n            } else {\n                if (this.statusLabel) {\n                    this.statusLabel.string = \"GamePageController中没有找到showGameStartAnimation方法\";\n                }\n            }\n        } else {\n            if (this.statusLabel) {\n                this.statusLabel.string = \"未找到GamePageController实例\";\n            }\n        }\n    }\n\n    /**\n     * 测试回合开始动画\n     */\n    testRoundStartAnimation() {\n        if (this.statusLabel) {\n            this.statusLabel.string = \"测试回合开始动画...\";\n        }\n\n        // 获取GamePageController实例\n        const gamePageController = (window as any).gamePageController;\n        if (gamePageController) {\n            // 调用回合开始动画\n            if (gamePageController.showRoundStartAnimation) {\n                gamePageController.showRoundStartAnimation();\n                if (this.statusLabel) {\n                    this.statusLabel.string = \"回合开始动画已触发\";\n                }\n            } else {\n                if (this.statusLabel) {\n                    this.statusLabel.string = \"GamePageController中没有找到showRoundStartAnimation方法\";\n                }\n            }\n        } else {\n            if (this.statusLabel) {\n                this.statusLabel.string = \"未找到GamePageController实例\";\n            }\n        }\n    }\n\n    // 测试AI托管状态变更消息\n    testAIStatusChange() {\n        // 获取当前用户ID，用于测试\n        const currentUserId = GlobalBean.GetInstance().loginData?.userInfo?.userId || \"player_001\";\n\n        const testData: AIStatusChange = {\n            userId: currentUserId, // 使用当前用户ID，这样会显示托管页面\n            isAIManaged: true,\n            timestamp: Date.now()\n        };\n\n        const messageBean = {\n            msgId: MessageId.MsgTypeAIStatusChange,\n            code: 0,\n            msg: \"success\",\n            data: testData\n        };\n\n        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);\n\n        if (this.statusLabel) {\n            this.statusLabel.string = `已发送AI托管状态变更: ${currentUserId}进入托管，应显示托管页面`;\n        }\n\n        // 5秒后发送退出托管的消息（给用户足够时间测试点击）\n        this.scheduleOnce(() => {\n            const exitData: AIStatusChange = {\n                userId: currentUserId,\n                isAIManaged: false,\n                timestamp: Date.now()\n            };\n\n            const exitMessageBean = {\n                msgId: MessageId.MsgTypeAIStatusChange,\n                code: 0,\n                msg: \"success\",\n                data: exitData\n            };\n\n            GameMgr.Event.Send(EventType.ReceiveMessage, exitMessageBean);\n\n            if (this.statusLabel) {\n                this.statusLabel.string = `已发送AI托管状态变更: ${currentUserId}退出托管，应隐藏托管页面`;\n            }\n        }, 5.0);\n    }\n\n    onDestroy() {\n        if (this.testButton) {\n            this.testButton.node.off('click', this.sendTestMessage, this);\n        }\n        if (this.firstChoiceTestButton) {\n            this.firstChoiceTestButton.node.off('click', this.testFirstChoiceBonusFlow, this);\n        }\n        if (this.testGameStartAnimationBtn) {\n            this.testGameStartAnimationBtn.node.off('click', this.testGameStartAnimation, this);\n        }\n        if (this.testRoundStartAnimationBtn) {\n            this.testRoundStartAnimationBtn.node.off('click', this.testRoundStartAnimation, this);\n        }\n        if (this.testAIStatusChangeBtn) {\n            this.testAIStatusChangeBtn.node.off('click', this.testAIStatusChange, this);\n        }\n    }\n}\n"]}