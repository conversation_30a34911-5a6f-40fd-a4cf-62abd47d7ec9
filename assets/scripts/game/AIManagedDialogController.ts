// AI托管中页面控制器
// 当玩家进入AI托管状态时显示，点击屏幕任何位置发送取消托管消息

import { WebSocketManager } from "../net/WebSocketManager";
import { MessageId } from "../net/MessageId";
import { Config } from "../util/Config";

const { ccclass, property } = cc._decorator;

@ccclass
export default class AIManagedDialogController extends cc.Component {

    @property(cc.Node)
    boardBg: cc.Node = null; // 背景板

    @property(cc.Node)
    maskNode: cc.Node = null; // 遮罩节点，用于接收点击事件

   

    private isShowing: boolean = false; // 是否正在显示

    start() {
        // 初始化时隐藏
        this.node.active = false;
        
        // 为遮罩节点添加点击事件监听
        if (this.maskNode) {
            this.maskNode.on(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);
        }
    }

    /**
     * 显示托管中页面
     */
    show() {
        if (this.isShowing) {
            return;
        }

        this.isShowing = true;
        this.node.active = true;

       

        // 初始化动画状态
        if (this.boardBg) {
            this.boardBg.scale = 0;
            this.boardBg.opacity = 0;
        }

        // 执行显示动画
        this.playShowAnimation();
    }

    /**
     * 隐藏托管中页面
     */
    hide() {
        if (!this.isShowing) {
            return;
        }

        this.isShowing = false;

        // 执行隐藏动画
        this.playHideAnimation(() => {
            this.node.active = false;
        });
    }

    /**
     * 播放显示动画
     */
    private playShowAnimation() {
        if (!this.boardBg) {
            return;
        }

        // 缩放和透明度动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { 
                scale: 1, 
                opacity: 255 
            }, { 
                easing: 'backOut' 
            })
            .start();
    }

    /**
     * 播放隐藏动画
     * @param callback 动画完成回调
     */
    private playHideAnimation(callback?: () => void) {
        if (!this.boardBg) {
            if (callback) callback();
            return;
        }

        // 缩放和透明度动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { 
                scale: 0, 
                opacity: 0 
            }, { 
                easing: 'backIn' 
            })
            .call(() => {
                if (callback) callback();
            })
            .start();
    }

    /**
     * 遮罩点击事件处理
     */
    private onMaskClick() {
        if (!this.isShowing) {
            return;
        }

        console.log("点击屏幕，发送取消AI托管消息");
        
        // 发送取消AI托管消息
        this.sendCancelAIManagement();
        
        // 立即隐藏页面（不等待服务器响应）
        this.hide();
    }

    /**
     * 发送取消AI托管消息
     */
    private sendCancelAIManagement() {
        const cancelData = {
            // 可以根据需要添加其他参数
        };

        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeCancelAIManagement, cancelData);
        console.log("已发送取消AI托管消息");
    }

    /**
     * 检查是否正在显示
     */
    isVisible(): boolean {
        return this.isShowing && this.node.active;
    }

    onDestroy() {
        // 清理事件监听
        if (this.maskNode) {
            this.maskNode.off(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);
        }
    }
}
