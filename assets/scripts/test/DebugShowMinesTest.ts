// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { MessageId } from "../net/MessageId";
import { WebSocketManager } from "../net/WebSocketManager";
import { GameMgr } from "../common/GameMgr";
import { EventType } from "../common/EventCenter";

const { ccclass, property } = cc._decorator;

@ccclass
export default class DebugShowMinesTest extends cc.Component {

    @property(cc.Button)
    testButton: cc.Button = null;

    @property(cc.Label)
    statusLabel: cc.Label = null;

    start() {
        if (this.testButton) {
            this.testButton.node.on('click', this.sendDebugShowMinesMessage, this);
        }

        if (this.statusLabel) {
            this.statusLabel.string = '点击按钮测试DebugShowMines消息';
        }
    }

    // 发送测试的DebugShowMines消息
    sendDebugShowMinesMessage() {
        cc.log("发送DebugShowMines测试消息");
        
        // 发送DebugShowMines消息到服务器
        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeDebugShowMines, {});

        // 模拟服务器响应（用于测试）
        this.scheduleOnce(() => {
            this.simulateServerResponse();
        }, 1.0);

        if (this.statusLabel) {
            this.statusLabel.string = '已发送DebugShowMines消息';
        }
    }

    // 模拟服务器响应
    private simulateServerResponse() {
        // 创建模拟的地雷位置数据
        const testMinePositions = [
            { x: 1, y: 1 },
            { x: 3, y: 2 },
            { x: 5, y: 4 },
            { x: 2, y: 6 },
            { x: 7, y: 3 }
        ];

        // 模拟接收到的消息格式
        const messageBean = {
            msgId: MessageId.MsgTypeDebugShowMines,
            code: 0,
            msg: "success",
            data: testMinePositions
        };

        // 发送消息事件
        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);

        if (this.statusLabel) {
            this.statusLabel.string = `模拟响应：显示${testMinePositions.length}个地雷位置`;
        }
    }

    onDestroy() {
        if (this.testButton) {
            this.testButton.node.off('click', this.sendDebugShowMinesMessage, this);
        }
    }
}
