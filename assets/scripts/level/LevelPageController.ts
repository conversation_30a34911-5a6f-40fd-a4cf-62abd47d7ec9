// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { ExtendLevelInfoResponse } from "../bean/GameBean";
import LeaveDialogController from "../hall/LeaveDialogController";
import { Tools } from "../util/Tools";
import { Config } from "../util/Config";
import GlobalManagerController, { PageType } from "../GlobalManagerController";
import SingleChessBoardController from "../game/Chess/SingleChessBoardController";
import { WebSocketManager } from "../net/WebSocketManager";
import { MessageId } from "../net/MessageId";
import { GameMgr } from "../common/GameMgr";
import { EventType } from "../common/EventCenter";
import { ReceivedMessageBean } from "../net/MessageBaseBean";

const { ccclass, property } = cc._decorator;

@ccclass
export default class LevelPageController extends cc.Component {

    // 返回按钮
    @property(cc.Node)
    backButton: cc.Node = null;

    // 开始游戏按钮
    @property(cc.Button)
    startGameButton: cc.Button = null;

    // 地雷数UI标签
    @property(cc.Label)
    mineCountLabel: cc.Label = null;

    // 当前关卡数UI标签
    @property(cc.Label)
    currentLevelLabel: cc.Label = null;

    // 退出游戏弹窗
    @property(LeaveDialogController)
    leaveDialogController: LeaveDialogController = null;

    // level_page节点
    @property(cc.Node)
    levelPageNode: cc.Node = null;

    // game_map_1节点
    @property(cc.Node)
    gameMap1Node: cc.Node = null;

    // game_map_2节点
    @property(cc.Node)
    gameMap2Node: cc.Node = null;

    // 方形地图节点引用
    @property(cc.Node)
    qipan8x8Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan8*8

    @property(cc.Node)
    qipan8x9Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan8*9

    @property(cc.Node)
    qipan9x9Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan9*9

    @property(cc.Node)
    qipan9x10Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan9*10

    @property(cc.Node)
    qipan10x10Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan10*10

    // 特殊关卡节点引用
    @property(cc.Node)
    levelS001Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S001

    @property(cc.Node)
    levelS002Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S002

    @property(cc.Node)
    levelS003Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S003

    @property(cc.Node)
    levelS004Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S004

    @property(cc.Node)
    levelS005Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S005

    @property(cc.Node)
    levelS006Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S006

    // 单机模式棋盘控制器
    @property(SingleChessBoardController)
    singleChessBoardController: SingleChessBoardController = null;

    // 测试按钮（用于调试显示地雷位置）
    @property(cc.Button)
    debugShowMinesButton: cc.Button = null;

    // 测试预制体（用于显示地雷位置）
    @property(cc.Prefab)
    debugMinePrefab: cc.Prefab = null;

    // 结算页面相关节点
    @property(cc.Node)
    levelSettlementNode: cc.Node = null; // level_settlement节点

    @property(cc.Node)
    boardBgNode: cc.Node = null; // level_settlement/board_bg节点

    @property(cc.Node)
    loseBgNode: cc.Node = null; // level_settlement/board_bg/lose_bg节点

    @property(cc.Node)
    winBgNode: cc.Node = null; // level_settlement/board_bg/win_bg节点

    @property(cc.Button)
    retryButton: cc.Button = null; // 再来一次按钮

    @property(cc.Button)
    nextLevelButton: cc.Button = null; // 下一关按钮

    // 当前关卡数据
    private currentLevel: number = 1;
    private currentLevelInfo: ExtendLevelInfoResponse = null;
    private currentRoomId: number = 0; // 当前关卡游戏的房间ID
    private currentSingleChessBoard: SingleChessBoardController = null; // 当前激活的单机棋盘

    // 性能优化相关
    private lastShownMapNode: cc.Node = null; // 记录上次显示的地图节点
    private isUpdating: boolean = false; // 防止重复更新

    onLoad() {
        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式
        if (this.backButton) {
            Tools.imageButtonClick(this.backButton, Config.buttonRes + 'side_btn_back_normal', Config.buttonRes + 'side_btn_back_pressed', () => {
                this.onBackButtonClick();
            });
        }

        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }

        // 注册单机模式消息监听
        this.registerSingleModeMessageHandlers();

        // 设置结算页面按钮事件
        this.setupSettlementButtons();

        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新
    }

    start() {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();

        // 设置测试按钮点击事件
        this.setupDebugButton();
    }

    /**
     * 返回按钮点击事件
     */
    private onBackButtonClick() {
        // 如果没有有效的房间ID，说明还没有进入游戏，直接返回关卡选择页面
        if (this.currentRoomId <= 0) {
           
            this.returnToLevelSelect();
            return;
        }

        // 弹出确认退出对话框，type=1表示退出本局游戏，传递当前房间ID
        if (this.leaveDialogController) {

            this.leaveDialogController.show(1, () => {

            }, this.currentRoomId);
        } else {
            cc.warn("LeaveDialogController 未配置");
        }
    }

    /**
     * 返回到关卡选择页面
     */
    private returnToLevelSelect() {
        // 查找GlobalManagerController并切换到大厅页面
        const globalManagerNode = cc.find("Canvas/global_node") || cc.find("global_node");
        if (globalManagerNode) {
            const globalManager = globalManagerNode.getComponent(GlobalManagerController);
            if (globalManager) {
                globalManager.setCurrentPage(PageType.HALL_PAGE);
            }
        }
    }

    /**
     * 开始游戏按钮点击事件
     */
    private onStartGameButtonClick() {
        // ExtendLevelInfo消息现在由LevelSelectPageController发送
        // 这里直接进入游戏，等待后端响应
        
    }

    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    public onExtendLevelInfo(levelInfo: ExtendLevelInfoResponse) {
       

        this.currentLevelInfo = levelInfo;

        // 保存房间ID，用于退出时使用
        if (levelInfo.roomId) {
            this.currentRoomId = levelInfo.roomId;
        }

        // 开始新游戏时，先重置当前棋盘（清理上一局的痕迹）
        if (this.currentSingleChessBoard) {
           
            this.currentSingleChessBoard.resetBoard();
        }

        // 更新地雷数UI
        this.updateMineCountUI(levelInfo.mineCount);

        // 使用当前设置的关卡编号，而不是后端返回的levelId
        // 因为后端的levelId可能与前端的关卡编号不一致

        this.enterLevel(this.currentLevel);

        // 通知当前激活的单机棋盘控制器处理ExtendLevelInfo
        if (this.currentSingleChessBoard) {
            this.currentSingleChessBoard.onExtendLevelInfo();
        }
    }

    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    private updateMineCountUI(mineCount: number) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
           
        }
    }

    /**
     * 更新当前关卡数UI
     * @param levelNumber 关卡编号
     */
    private updateCurrentLevelUI(levelNumber: number) {
        if (this.currentLevelLabel) {
            this.currentLevelLabel.string = `第${levelNumber}关`;
           
        }
    }

    /**
     * 根据关卡数进入相应的关卡（优化版本）
     * @param levelNumber 关卡编号
     */
    private enterLevel(levelNumber: number) {
        // 防止重复更新
        if (this.isUpdating) {
            return;
        }
        this.isUpdating = true;

        // 更新关卡数UI显示
        this.updateCurrentLevelUI(levelNumber);

        // 获取目标地图节点和容器
        const targetMapInfo = this.getMapNodeByLevel(levelNumber);
        if (!targetMapInfo) {
            cc.warn(`未知的关卡编号: ${levelNumber}`);
            this.isUpdating = false;
            return;
        }

        // 只有当目标节点与当前显示的节点不同时才进行切换
        if (this.lastShownMapNode !== targetMapInfo.mapNode) {
            // 隐藏上一个显示的地图节点
            if (this.lastShownMapNode) {
                this.lastShownMapNode.active = false;
            }

            // 显示目标容器和地图节点
            this.showMapContainer(targetMapInfo.containerType);
            this.showMapNodeOptimized(targetMapInfo.mapNode, targetMapInfo.mapName);

            // 记录当前显示的节点
            this.lastShownMapNode = targetMapInfo.mapNode;
        }

        // 设置当前激活的单机棋盘控制器
        if (this.singleChessBoardController) {
            // 根据关卡设置棋盘类型
            const boardType = this.getBoardTypeByLevel(levelNumber);
            this.singleChessBoardController.initBoard(boardType);
            this.currentSingleChessBoard = this.singleChessBoardController;
        } else {
            this.currentSingleChessBoard = null;
        }

        this.isUpdating = false;
    }

    /**
     * 根据关卡数获取对应的地图节点信息
     * @param levelNumber 关卡编号
     */
    private getMapNodeByLevel(levelNumber: number): {mapNode: cc.Node, mapName: string, containerType: 'map1' | 'map2'} | null {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return {mapNode: this.qipan8x8Node, mapName: "qipan8*8", containerType: 'map1'};
        } else if (levelNumber === 5) {
            return {mapNode: this.levelS001Node, mapName: "Level_S001", containerType: 'map2'};
        } else if (levelNumber >= 6 && levelNumber <= 9) {
            return {mapNode: this.qipan8x9Node, mapName: "qipan8*9", containerType: 'map1'};
        } else if (levelNumber === 10) {
            return {mapNode: this.levelS002Node, mapName: "Level_S002", containerType: 'map2'};
        } else if (levelNumber >= 11 && levelNumber <= 14) {
            return {mapNode: this.qipan9x9Node, mapName: "qipan9*9", containerType: 'map1'};
        } else if (levelNumber === 15) {
            return {mapNode: this.levelS003Node, mapName: "Level_S003", containerType: 'map2'};
        } else if (levelNumber >= 16 && levelNumber <= 19) {
            return {mapNode: this.qipan9x10Node, mapName: "qipan9*10", containerType: 'map1'};
        } else if (levelNumber === 20) {
            return {mapNode: this.levelS004Node, mapName: "Level_S004", containerType: 'map2'};
        } else if (levelNumber >= 21 && levelNumber <= 24) {
            return {mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1'};
        } else if (levelNumber === 25) {
            return {mapNode: this.levelS005Node, mapName: "Level_S005", containerType: 'map2'};
        } else if (levelNumber >= 26 && levelNumber <= 29) {
            return {mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1'};
        } else if (levelNumber === 30) {
            return {mapNode: this.levelS006Node, mapName: "Level_S006", containerType: 'map2'};
        }
        return null;
    }



    /**
     * 根据关卡编号获取棋盘类型
     * @param levelNumber 关卡编号
     */
    private getBoardTypeByLevel(levelNumber: number): string {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return "8x8";
        } else if (levelNumber >= 6 && levelNumber <= 9) {
            return "8x9";
        } else if (levelNumber >= 11 && levelNumber <= 14) {
            return "9x9";
        } else if (levelNumber >= 16 && levelNumber <= 19) {
            return "9x10";
        } else if (levelNumber >= 21 && levelNumber <= 24 || levelNumber >= 26 && levelNumber <= 29) {
            return "10x10";
        }
        return "8x8"; // 默认
    }

    /**
     * 显示指定的地图节点（优化版本）
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    private showMapNodeOptimized(mapNode: cc.Node, mapName: string) {
        if (mapNode) {
            mapNode.active = true;
        } else {
            cc.warn(`❌ 地图节点未找到: ${mapName}`);
        }
    }

    /**
     * 显示指定的地图容器
     * @param containerType 容器类型
     */
    private showMapContainer(containerType: 'map1' | 'map2') {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }

        // 根据容器类型显示对应容器，隐藏另一个
        if (containerType === 'map1') {
            if (this.gameMap1Node && !this.gameMap1Node.active) {
                this.gameMap1Node.active = true;
            }
            if (this.gameMap2Node && this.gameMap2Node.active) {
                this.gameMap2Node.active = false;
            }
        } else {
            if (this.gameMap2Node && !this.gameMap2Node.active) {
                this.gameMap2Node.active = true;
            }
            if (this.gameMap1Node && this.gameMap1Node.active) {
                this.gameMap1Node.active = false;
            }
        }
    }

    /**
     * 隐藏所有地图节点
     */
    private hideAllMapNodes() {
        const allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];

        allMapNodes.forEach(node => {
            if (node) {
                node.active = false;
            }
        });
    }

    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    public setCurrentLevel(levelNumber: number) {
      
        this.currentLevel = levelNumber;


        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    }

    /**
     * 获取当前关卡编号
     */
    public getCurrentLevel(): number {
        return this.currentLevel;
    }

    /**
     * 获取当前关卡信息
     */
    public getCurrentLevelInfo(): ExtendLevelInfoResponse {
        return this.currentLevelInfo;
    }

    /**
     * 获取当前房间ID
     */
    public getCurrentRoomId(): number {
        return this.currentRoomId;
    }

    /**
     * 隐藏所有地图容器
     */
    private hideAllMapContainers() {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
          
        }

        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
            
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
           
        }

        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    }

    /**
     * 显示 game_map_1 容器（方形地图）
     */
    private showGameMap1() {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
            
        } else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    }

    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    private showGameMap2() {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;

        } else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    }

    /**
     * 获取当前激活的单机棋盘控制器
     */
    public getCurrentSingleChessBoard(): SingleChessBoardController | null {
        return this.currentSingleChessBoard;
    }

    /**
     * 处理单机模式的点击响应
     * @param response 点击响应数据
     */
    public handleSingleModeClickResponse(response: any) {
        if (this.currentSingleChessBoard) {
            const { x, y, result, chainReaction } = response;

            // 处理点击结果
            if (x !== undefined && y !== undefined && result !== undefined) {
                this.currentSingleChessBoard.handleClickResponse(x, y, result);
            }

            // 处理连锁反应
            if (chainReaction && Array.isArray(chainReaction)) {
                this.currentSingleChessBoard.handleChainReaction(chainReaction);
            }
        }
    }

    /**
     * 处理单机模式游戏结束
     * @param gameEndData 游戏结束数据
     */
    public handleSingleModeGameEnd(gameEndData: any) {
        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();

            // 处理游戏结束
            this.currentSingleChessBoard.onLevelGameEnd();
        }
    }

    /**
     * 重置当前单机棋盘（仅在开始新游戏时调用）
     */
    public resetCurrentSingleChessBoard() {
      
        if (this.currentSingleChessBoard) {
            // 重置棋盘状态（清理所有预制体和格子状态）
            this.currentSingleChessBoard.resetBoard();

            // 重新启用触摸事件
            this.currentSingleChessBoard.enableAllGridTouch();
        }
    }

    /**
     * 注册单机模式消息处理器
     */
    private registerSingleModeMessageHandlers() {
        // 监听WebSocket消息
        GameMgr.Event.AddEventListener(EventType.ReceiveMessage, this.onReceiveMessage, this);
    }

    /**
     * 取消单机模式消息监听
     */
    private unregisterSingleModeMessageHandlers() {
        GameMgr.Event.RemoveEventListener(EventType.ReceiveMessage, this.onReceiveMessage, this);
    }

    /**
     * 处理接收到的WebSocket消息
     * @param messageBean 消息数据
     */
    private onReceiveMessage(messageBean: ReceivedMessageBean) {
        switch (messageBean.msgId) {
            case MessageId.MsgTypeLevelClickBlock:
                this.onLevelClickBlockResponse(messageBean.data);
                break;
            case MessageId.MsgTypeLevelGameEnd:
                this.onLevelGameEnd(messageBean.data);
                break;
        }
    }

    /**
     * 处理LevelClickBlock响应
     * @param response 点击响应数据
     */
    public onLevelClickBlockResponse(response: any) {
        

        if (this.currentSingleChessBoard) {
            // 解构响应数据，支持多种可能的字段名
            const {
                x, y, result, action,
                chainReaction, revealedGrids, floodFill, revealedBlocks,
                floodFillResults  // 单机模式使用这个字段
            } = response;

            // 根据action类型处理不同的响应
            if (x !== undefined && y !== undefined && result !== undefined) {

                if (action === 2) {
                    // 标记/取消标记操作，不调用handleClickResponse，避免格子消失

                    // 不调用 handleClickResponse，因为标记操作不应该隐藏格子
                } else if (action === 1) {
                    // 挖掘操作

                    this.currentSingleChessBoard.handleClickResponse(x, y, result);

                    // 处理连锁展开数据
                    if (floodFillResults && Array.isArray(floodFillResults) && floodFillResults.length > 0) {
                        
                        this.currentSingleChessBoard.handleFloodFillResults(floodFillResults);
                    }
                } else {
                    // 其他操作，默认按挖掘处理

                    this.currentSingleChessBoard.handleClickResponse(x, y, result);
                }
            }

         

            

            
        }
    }

    /**
     * 处理LevelGameEnd通知
     * @param gameEndData 游戏结束数据
     */
    public onLevelGameEnd(gameEndData: any) {
       

        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();

            // 处理游戏结束（不清理数据）
            this.currentSingleChessBoard.onLevelGameEnd();
        }

        // 显示结算页面
        this.showLevelSettlement(gameEndData);
    }

    /**
     * 设置结算页面按钮事件
     */
    private setupSettlementButtons() {
        // 再来一次按钮
        if (this.retryButton) {
            this.retryButton.node.on('click', this.onRetryButtonClick, this);
        }

        // 下一关按钮
        if (this.nextLevelButton) {
            this.nextLevelButton.node.on('click', this.onNextLevelButtonClick, this);
        }
    }

    /**
     * 显示结算页面
     * @param gameEndData 游戏结束数据
     */
    private showLevelSettlement(gameEndData: any) {
       
        if (!this.levelSettlementNode) {
            console.error("levelSettlementNode 未配置");
            return;
        }

        // 显示结算页面
        this.levelSettlementNode.active = true;

        // 根据游戏结果显示对应的背景 - 修复成功判断逻辑
        const isSuccess = gameEndData.isSuccess || gameEndData.success || gameEndData.isWin || gameEndData.gameStatus === 1;

       

        if (isSuccess) {
            // 成功 - 显示胜利背景
            if (this.winBgNode) {
                this.winBgNode.active = true;
            }
            if (this.loseBgNode) {
                this.loseBgNode.active = false;
            }
            
        } else {
            // 失败 - 显示失败背景
            if (this.loseBgNode) {
                this.loseBgNode.active = true;
            }
            if (this.winBgNode) {
                this.winBgNode.active = false;
            }
           
        }
    }

    /**
     * 再来一次按钮点击事件
     */
    private onRetryButtonClick() {
       

        // 关闭结算页面
        this.hideLevelSettlement();

        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹

        // 发送当前关卡的ExtendLevelInfo
        this.sendExtendLevelInfo(this.currentLevel);
    }

    /**
     * 下一关按钮点击事件
     */
    private onNextLevelButtonClick() {
        

        // 关闭结算页面
        this.hideLevelSettlement();

        // 进入下一关
        const nextLevel = this.currentLevel + 1;
        this.setCurrentLevel(nextLevel);

        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹

        // 发送下一关的ExtendLevelInfo
        this.sendExtendLevelInfo(nextLevel);
    }

    /**
     * 隐藏结算页面
     */
    private hideLevelSettlement() {
        if (this.levelSettlementNode) {
            this.levelSettlementNode.active = false;
        }
    }

    /**
     * 发送ExtendLevelInfo消息
     * @param levelId 关卡ID
     */
    private sendExtendLevelInfo(levelId: number) {
       

        const request = {
            levelId: levelId
        };

        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeExtendLevelInfo, request);
    }

    /**
     * 设置测试按钮
     */
    private setupDebugButton() {
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.on('click', this.onDebugShowMinesClick, this);
        }
    }

    /**
     * 测试按钮点击事件 - 发送DebugShowMines消息
     */
    private onDebugShowMinesClick() {
        // 只在单机模式下工作
        if (!this.isInSingleMode()) {
            cc.warn("测试功能只在单机模式下可用");
            return;
        }

        cc.log("发送DebugShowMines消息");
        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeDebugShowMines, {});
    }

    /**
     * 判断是否在单机模式
     */
    private isInSingleMode(): boolean {
        // 单机模式的判断逻辑：当前页面是关卡页面且有有效的房间ID
        return this.currentRoomId > 0;
    }

    /**
     * 处理DebugShowMines响应，在炸弹位置生成测试预制体
     * @param minePositions 炸弹位置数组 [{x: number, y: number}]
     */
    public handleDebugShowMines(minePositions: Array<{x: number, y: number}>) {
        cc.log("=== handleDebugShowMines 被调用 ===");
        cc.log("接收到的地雷位置数据:", minePositions);
        cc.log("debugMinePrefab 是否存在:", !!this.debugMinePrefab);
        cc.log("currentSingleChessBoard 是否存在:", !!this.currentSingleChessBoard);

        if (!this.debugMinePrefab) {
            cc.warn("debugMinePrefab 预制体未设置，无法显示测试标记");
            return;
        }

        if (!this.currentSingleChessBoard) {
            cc.warn("当前没有激活的单机棋盘");
            return;
        }

        if (!minePositions || !Array.isArray(minePositions) || minePositions.length === 0) {
            cc.warn("地雷位置数据无效:", minePositions);
            return;
        }

        cc.log("收到地雷位置数据，开始生成测试预制体:", minePositions);
        cc.log("地雷位置数组长度:", minePositions.length);

        // 先尝试直接创建一个测试预制体，不使用延迟
        cc.log("=== 尝试直接创建第一个测试预制体 ===");
        if (minePositions.length > 0) {
            const firstPosition = minePositions[0];
            cc.log("第一个位置:", firstPosition);
            cc.log("第一个位置的x:", firstPosition.x, "类型:", typeof firstPosition.x);
            cc.log("第一个位置的y:", firstPosition.y, "类型:", typeof firstPosition.y);

            // 直接调用，不使用延迟
            this.createDebugMinePrefab(firstPosition.x, firstPosition.y);
        }

        // 在每个炸弹位置生成测试预制体
        minePositions.forEach((position, index) => {
            cc.log(`准备在位置 (${position.x}, ${position.y}) 生成测试预制体，延迟 ${index * 0.1} 秒`);
            cc.log(`position 对象:`, position);
            cc.log(`position.x:`, position.x, `position.y:`, position.y);

            if (index === 0) {
                // 第一个不延迟，立即执行
                cc.log(`立即创建第一个测试预制体`);
                this.createDebugMinePrefab(position.x, position.y);
            } else {
                // 其他的使用延迟
                this.scheduleOnce(() => {
                    cc.log(`延迟创建测试预制体，位置: (${position.x}, ${position.y})`);
                    this.createDebugMinePrefab(position.x, position.y);
                }, index * 0.1);
            }
        });
    }

    /**
     * 在指定位置创建测试预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    private createDebugMinePrefab(x: number, y: number) {
        cc.log(`=== createDebugMinePrefab 被调用，位置: (${x}, ${y}) ===`);

        if (!this.debugMinePrefab) {
            cc.error("debugMinePrefab 为空，无法创建测试预制体");
            return;
        }

        if (!this.currentSingleChessBoard) {
            cc.error("currentSingleChessBoard 为空，无法创建测试预制体");
            return;
        }

        cc.log("开始调用 createCustomPrefab 方法");

        try {
            // 使用棋盘控制器的公共方法创建自定义预制体
            const debugNode = this.currentSingleChessBoard.createCustomPrefab(
                x, y,
                this.debugMinePrefab,
                `DebugMine_${x}_${y}`
            );

            if (debugNode) {
                cc.log(`✅ 成功在位置 (${x}, ${y}) 创建了测试预制体:`, debugNode.name);
            } else {
                cc.error(`❌ 在位置 (${x}, ${y}) 创建测试预制体失败，返回值为空`);
            }
        } catch (error) {
            cc.error(`❌ 创建测试预制体时发生错误:`, error);
        }
    }

    onDestroy() {
        // 取消消息监听
        this.unregisterSingleModeMessageHandlers();

        // 清理按钮事件
        if (this.retryButton) {
            this.retryButton.node.off('click', this.onRetryButtonClick, this);
        }
        if (this.nextLevelButton) {
            this.nextLevelButton.node.off('click', this.onNextLevelButtonClick, this);
        }
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.off('click', this.onDebugShowMinesClick, this);
        }
    }
}
