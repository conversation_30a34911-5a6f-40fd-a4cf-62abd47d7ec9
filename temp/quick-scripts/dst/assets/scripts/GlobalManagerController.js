
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/GlobalManagerController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '00371cQlglDVIdse39v0U2E', 'GlobalManagerController');
// scripts/GlobalManagerController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PageType = void 0;
var MeshTools_1 = require("../meshTools/MeshTools");
var Publish_1 = require("../meshTools/tools/Publish");
var EnumBean_1 = require("./bean/EnumBean");
var GlobalBean_1 = require("./bean/GlobalBean");
var LanguageType_1 = require("./bean/LanguageType");
var EventCenter_1 = require("./common/EventCenter");
var GameMgr_1 = require("./common/GameMgr");
var GamePageController_1 = require("./game/GamePageController");
var HallPageController_1 = require("./hall/HallPageController");
var LevelPageController_1 = require("./level/LevelPageController");
var TopUpDialogController_1 = require("./hall/TopUpDialogController");
var ErrorCode_1 = require("./net/ErrorCode");
var GameServerUrl_1 = require("./net/GameServerUrl");
var MessageBaseBean_1 = require("./net/MessageBaseBean");
var MessageId_1 = require("./net/MessageId");
var WebSocketManager_1 = require("./net/WebSocketManager");
var WebSocketTool_1 = require("./net/WebSocketTool");
var StartUpPageController_1 = require("./start_up/StartUpPageController");
var TipsDialogController_1 = require("./TipsDialogController");
var ToastController_1 = require("./ToastController");
var AudioMgr_1 = require("./util/AudioMgr");
var Config_1 = require("./util/Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
window.languageName = "en";
var PageType;
(function (PageType) {
    PageType[PageType["START_UP_PAGE"] = 0] = "START_UP_PAGE";
    PageType[PageType["HALL_PAGE"] = 1] = "HALL_PAGE";
    PageType[PageType["GAME_PAGE"] = 2] = "GAME_PAGE";
    PageType[PageType["LEVEL_PAGE"] = 3] = "LEVEL_PAGE";
})(PageType = exports.PageType || (exports.PageType = {}));
var GlobalManagerController = /** @class */ (function (_super) {
    __extends(GlobalManagerController, _super);
    function GlobalManagerController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.tipsDialogController = null; //这个是错误弹窗 只有一个退出按钮
        _this.topUpDialogController = null; //充值弹窗
        _this.netError = null; //这个是断网的时候展示的转圈的
        _this.toastController = null; //toast 的布局
        _this.startUpPage = null; //启动页
        _this.hallPage = null; //大厅页
        _this.gamePage = null; //游戏页面
        _this.levelPage = null; //关卡页面
        _this.currentPage = PageType.START_UP_PAGE; //当前展示的页面，默认展示的是启动页面
        _this.startUpPageController = null; //启动页面的总管理器
        _this.hallPageController = null; //大厅页面的总管理器
        _this.gamePageController = null; //游戏页面的总管理器
        _this.levelPageController = null; //关卡页面的总管理器
        return _this;
        // update (dt) {}
    }
    GlobalManagerController.prototype.onLoad = function () {
        cc.resources.preloadDir(Config_1.Config.hallRes, cc.SpriteFrame); //提前预加载大厅图片资源
        // 获取音频管理器实例
        var audioMgr = AudioMgr_1.AudioMgr.ins;
        // 初始化音频管理器（如果还未初始化）
        audioMgr.init();
        cc.debug.setDisplayStats(false);
        //获取URL拼接渠道参数
        this.getUrlParams();
        GameMgr_1.GameMgr.H5SDK.AddAPPEvent();
        this.getAppConfig();
        cc.game.on(cc.game.EVENT_SHOW, function () {
            GameMgr_1.GameMgr.Console.Log("EVENT_SHOW");
            GameMgr_1.GameMgr.GameData.GameIsInFront = true;
            // 触发重连
            WebSocketTool_1.WebSocketTool.GetInstance().atOnceReconnect();
        }, this);
        cc.game.on(cc.game.EVENT_HIDE, function () {
            GameMgr_1.GameMgr.Console.Log("EVENT_HIDE");
            GameMgr_1.GameMgr.GameData.GameIsInFront = false;
            // 断开WebSocket连接
            WebSocketTool_1.WebSocketTool.GetInstance().disconnect();
        }, this);
        //这里监听程序内消息
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.AutoMessage, this.onAutoMessage, this);
        //这里监听长链接消息（异常）
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveErrorMessage, this.onErrorMessage, this);
        //这里监听长链接消息（正常）
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveMessage, this.onMessage, this);
        this.setCurrentPage(PageType.START_UP_PAGE);
        this.startUpPageController = this.startUpPage.getComponent(StartUpPageController_1.default);
        this.hallPageController = this.hallPage.getComponent(HallPageController_1.default);
        this.gamePageController = this.gamePage.getComponent(GamePageController_1.default);
        this.levelPageController = this.levelPage.getComponent(LevelPageController_1.default);
    };
    GlobalManagerController.prototype.onEnable = function () {
    };
    GlobalManagerController.prototype.onDestroy = function () {
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.AutoMessage, this.onAutoMessage, this);
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveErrorMessage, this.onErrorMessage, this);
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveMessage, this.onMessage, this);
    };
    GlobalManagerController.prototype.getAppConfig = function () {
        var _this = this;
        GameMgr_1.GameMgr.H5SDK.GetConfig(function (config) {
            var _a, _b, _c;
            MeshTools_1.MeshTools.Publish.appChannel = String(config.appChannel);
            MeshTools_1.MeshTools.Publish.appId = parseInt(config.appId);
            MeshTools_1.MeshTools.Publish.gameMode = String(config.gameMode);
            MeshTools_1.MeshTools.Publish.roomId = (_a = String(config.roomId)) !== null && _a !== void 0 ? _a : "";
            MeshTools_1.MeshTools.Publish.currencyIcon = (_c = (_b = config === null || config === void 0 ? void 0 : config.gameConfig) === null || _b === void 0 ? void 0 : _b.currencyIcon) !== null && _c !== void 0 ? _c : "";
            MeshTools_1.MeshTools.Publish.code = encodeURIComponent(config.code);
            MeshTools_1.MeshTools.Publish.userId = String(config.userId);
            MeshTools_1.MeshTools.Publish.language = String(config.language);
            MeshTools_1.MeshTools.Publish.gsp = config.gsp == undefined ? 101 : parseInt(config.gsp);
            _this.getHpptPath();
        });
    };
    GlobalManagerController.prototype.getHpptPath = function () {
        this.setLanguage(); //先设置语言
        if (!window.navigator.onLine) {
            this.showTips(window.getLocalizedStr('NetworkError'));
        }
        else {
            // // 获取游戏服务器地址
            // HttpManager.Instance.ReqServerUrl(() => {
            //     let httpUrl: string = GameServerUrl.Http;
            //     let wsUrl: string = GameServerUrl.Ws;
            //     if (httpUrl != "" || wsUrl != "") {
            //         WebSocketManager.GetInstance().connect();
            //     }
            // });
            GameServerUrl_1.GameServerUrl.Ws = "ws://************:2059/acceptor";
            WebSocketManager_1.WebSocketManager.GetInstance().connect();
        }
    };
    GlobalManagerController.prototype.getUrlParams = function () {
        var params = GameMgr_1.GameMgr.Utils.GetUrlParams(window.location.href); //获取当前页面的 url
        if (JSON.stringify(params) != "{}") {
            //@ts-ignore
            if (params.appChannel) {
                //@ts-ignore
                MeshTools_1.MeshTools.Publish.appChannel = params.appChannel;
                if (params.isDataByUrl) {
                    if (params.isDataByUrl === "true") {
                        MeshTools_1.MeshTools.Publish.appId = parseInt(params.appId);
                        MeshTools_1.MeshTools.Publish.gameMode = params.gameMode;
                        MeshTools_1.MeshTools.Publish.userId = params.userId;
                        MeshTools_1.MeshTools.Publish.code = params.code;
                        if (params.language) {
                            MeshTools_1.MeshTools.Publish.language = params.language;
                        }
                        if (params.roomId) {
                            MeshTools_1.MeshTools.Publish.roomId = params.roomId;
                        }
                        if (params.gsp) {
                            MeshTools_1.MeshTools.Publish.gsp = parseInt(params.gsp);
                        }
                        MeshTools_1.MeshTools.Publish.isDataByURL = true;
                    }
                }
            }
        }
    };
    GlobalManagerController.prototype.setLanguage = function () {
        switch (Publish_1.Publish.GetInstance().language) {
            case LanguageType_1.default.SimplifiedChinese: //简体中文
                window.languageName = LanguageType_1.default.SimplifiedChinese_type;
                break;
            case LanguageType_1.default.TraditionalChinese: //繁体中文
                window.languageName = LanguageType_1.default.TraditionalChinese_type;
                break;
            default: //默认是英语
                window.languageName = LanguageType_1.default.English_type;
                break;
        }
        window.refreshAllLocalizedComp();
    };
    GlobalManagerController.prototype.showTips = function (content) {
        this.tipsDialogController.showDialog(content, function () {
            GameMgr_1.GameMgr.H5SDK.CloseWebView();
        });
    };
    //程序内的通知消息
    GlobalManagerController.prototype.onAutoMessage = function (autoMessageBean) {
        switch (autoMessageBean.msgId) {
            case MessageBaseBean_1.AutoMessageId.JumpHallPage: //跳转进大厅页面
                this.setCurrentPage(PageType.HALL_PAGE);
                if (autoMessageBean.data.type === 1) { //1是启动页面跳转的 ，2 是玩家主动离开游戏房间
                    this.hallPageController.LoginSuccess(); //因为初始进来的时候是启动页面，大厅页面是隐藏状态，下面发送的消息收不到，所以需要主动调用一次
                }
                else if (autoMessageBean.data.type === 2) { //2 是玩家主动离开游戏房间，需要更新关卡进度
                    // 单机模式退出关卡后，请求ExtendLevelProgress更新关卡信息
                    WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelProgress, {});
                }
                break;
            case MessageBaseBean_1.AutoMessageId.ReconnectionFailureMsg: //长链接重连失败
                this.netError.active = false;
                this.showTips(window.getLocalizedStr('NetworkError'));
                break;
            case MessageBaseBean_1.AutoMessageId.LinkExceptionMsg: //长链接异常
                this.netError.active = true;
                break;
            case MessageBaseBean_1.AutoMessageId.GameRouteNotFoundMsg: //游戏线路异常的通知
                this.showTips(autoMessageBean.data.code);
                break;
            case MessageBaseBean_1.AutoMessageId.SwitchGameSceneMsg: //切换游戏场景
                this.setCurrentPage(PageType.GAME_PAGE);
                break;
            case MessageBaseBean_1.AutoMessageId.WalletUpdateMsg: //更新金豆余额的通知
                //发送获取更新用户信息的消息
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeUserInfo, {});
                break;
            case MessageBaseBean_1.AutoMessageId.ServerCodeUpdateMsg: //更新 code 的通知
                Publish_1.Publish.GetInstance().code = autoMessageBean.data.code;
                break;
        }
    };
    //长链接消息(异常)
    GlobalManagerController.prototype.onErrorMessage = function (messageBean) {
        switch (messageBean.code) {
            case ErrorCode_1.ErrorCode.ErrInvalidInviteCode: //无效的邀请码
                this.hallPageController.joinError();
                break;
            case ErrorCode_1.ErrorCode.ErrRequestUser: //获取用户信息失败
                this.showTips(window.getLocalizedStr('GetUserInfoFailed'));
                // 断开WebSocket连接
                WebSocketTool_1.WebSocketTool.GetInstance().disconnect();
                break;
            case ErrorCode_1.ErrorCode.ErrNotFoundRoom: //没有找到指定的房间
                if (messageBean.msgId != MessageId_1.MessageId.MsgTypeMoveBlock) {
                    this.toastController.showContent(window.getLocalizedStr('RoomDoesNotExist'));
                    //没有找到房间 就直接返回到大厅页面
                    this.setCurrentPage(PageType.HALL_PAGE);
                }
                break;
            case ErrorCode_1.ErrorCode.ErrNotFoundUser: // 没有找到玩家信息
                if (messageBean.msgId === MessageId_1.MessageId.MsgTypeEnterRoom) { //只有在这个messageId下 才会踢出到大厅
                    //没有找到玩家信息 就直接返回到大厅页面
                    this.setCurrentPage(PageType.HALL_PAGE);
                }
                break;
            case ErrorCode_1.ErrorCode.ErrEnoughUser: //房间已满
                this.toastController.showContent(window.getLocalizedStr('RoomIsFull'));
                break;
            case ErrorCode_1.ErrorCode.ErrChangeBalance: //扣除金币失败
            case ErrorCode_1.ErrorCode.ErrNotEnoughCoin: //金币不足
                this.topUpDialogController.show(function () { });
                break;
            case ErrorCode_1.ErrorCode.ErrPlaying: //玩家已经在游戏中了
                //执行一遍 enterroom
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeEnterRoom, {}); //重连进来的 玩家请求进入房间
                break;
        }
    };
    //长链接消息(正常)
    GlobalManagerController.prototype.onMessage = function (messageBean) {
        switch (messageBean.msgId) {
            case MessageId_1.MessageId.MsgTypeCreateWs: //创建ws连接 成功  
                this.netError.active = false;
                //登录
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLogin, {});
                break;
            case MessageId_1.MessageId.MsgTypeLogin: //获取登录数据并存储
                GlobalBean_1.GlobalBean.GetInstance().loginData = messageBean.data;
                if (this.currentPage === PageType.START_UP_PAGE) {
                    //判断当前是否是在启动页面
                    this.startUpPageController.setLogin();
                }
                else {
                    this.hallPageController.updateGold();
                    this.hallPageController.LoginSuccess();
                    //没有在游戏中，但是还停留在游戏页面
                    if (GlobalBean_1.GlobalBean.GetInstance().loginData.roomId === 0 && this.currentPage === PageType.GAME_PAGE) {
                        this.setCurrentPage(PageType.HALL_PAGE); //返回到大厅
                    }
                }
                break;
            case MessageId_1.MessageId.MsgTypePairRequest: //开始匹配
                this.hallPageController.setHallOrMatch(HallPageController_1.HallOrMatch.MATCH_PARENT);
                this.hallPageController.createMatchView();
                break;
            case MessageId_1.MessageId.MsgTypeCancelPair: //取消匹配
                this.hallPageController.setHallOrMatch(HallPageController_1.HallOrMatch.HALL_PARENT);
                break;
            case MessageId_1.MessageId.MsgTypeGameStart: //游戏开始
                var noticeStartGame = messageBean.data;
                GlobalBean_1.GlobalBean.GetInstance().noticeStartGame = noticeStartGame; //存储游戏数据
                var index = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users.findIndex(function (item) { return item.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId; }); //搜索
                //把游戏开始之后最新的金币余额进行赋值
                if (index != -1) {
                    GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.coin = noticeStartGame.users[index].coin;
                }
                // 处理游戏开始数据，获取炸弹数量和地图类型
                this.gamePageController.onGameStart(noticeStartGame);
                if (noticeStartGame.roomType === EnumBean_1.RoomType.RoomTypeCommon) { // 房间类型 1-普通场 2-私人场
                    this.hallPageController.setGameData();
                }
                else {
                    this.setCurrentPage(PageType.GAME_PAGE); //开始游戏进入游戏页面
                }
                break;
            case MessageId_1.MessageId.MsgTypeEnterRoom: //重连的游戏数据
                var noticeStartGame2 = messageBean.data;
                GlobalBean_1.GlobalBean.GetInstance().noticeStartGame = noticeStartGame2; //存储游戏数据
                // 处理重连游戏数据，获取炸弹数量和地图类型
                this.gamePageController.onGameStart(noticeStartGame2);
                //跳转进游戏页面
                this.setCurrentPage(PageType.GAME_PAGE);
                break;
            case MessageId_1.MessageId.MsgTypeLeaveRoom: // 玩家主动离开房间
                var leaveRoomData = messageBean.data;
                // 检查是否是关卡游戏的LeaveRoom响应（包含levelId字段）
                if (leaveRoomData.levelId !== undefined) {
                    // 关卡游戏的LeaveRoom响应，直接返回大厅
                    cc.log("收到关卡游戏退出响应，返回大厅页面");
                    GlobalBean_1.GlobalBean.GetInstance().cleanData(); //清空数据
                    var autoMessageBean = {
                        'msgId': MessageBaseBean_1.AutoMessageId.JumpHallPage,
                        'data': { 'type': 2 } //2 是玩家主动离开游戏房间
                    };
                    GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
                }
                else if (leaveRoomData.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId) {
                    // 普通房间游戏的LeaveRoom响应
                    GlobalBean_1.GlobalBean.GetInstance().cleanData(); //清空数据
                    var autoMessageBean = {
                        'msgId': MessageBaseBean_1.AutoMessageId.JumpHallPage,
                        'data': { 'type': 2 } //2 是玩家主动离开游戏房间
                    };
                    GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
                }
                break;
            case MessageId_1.MessageId.MsgTypeCreateInvite: //创建邀请（也就是创建私人游戏房间）
                GlobalBean_1.GlobalBean.GetInstance().inviteInfo = messageBean.data;
                //点击 create 的回调
                this.hallPageController.joinCreateRoom();
                break;
            case MessageId_1.MessageId.MsgTypeAcceptInvite: //接受邀请
                var acceptInvite = messageBean.data;
                GlobalBean_1.GlobalBean.GetInstance().inviteInfo = acceptInvite.inviteInfo;
                this.hallPageController.setAcceptInvite(acceptInvite);
                break;
            case MessageId_1.MessageId.MsgTypeLeaveInvite: //收到离开房间的信息
                var noticeLeaveInvite = messageBean.data;
                this.hallPageController.leaveRoom(noticeLeaveInvite);
                break;
            case MessageId_1.MessageId.MsgTypeInviteReady: //收到有玩家准备的消息
                var noticeUserInviteStatus = messageBean.data;
                this.hallPageController.setReadyState(noticeUserInviteStatus);
                break;
            case MessageId_1.MessageId.MsgTypeNoticeInviteStatus: //广播邀请状态
                var noticeUserInviteStatus2 = messageBean.data;
                this.hallPageController.setReadyState(noticeUserInviteStatus2);
                break;
            case MessageId_1.MessageId.MsgTypeInviteKickOut: //收到玩家被踢出的信息
                var inviteKickOut = messageBean.data;
                if (inviteKickOut.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId) {
                    this.toastController.showContent(window.getLocalizedStr('KickOut'));
                    //被踢的是自己的话 直接返回大厅
                    this.setCurrentPage(PageType.HALL_PAGE);
                }
                else {
                    //这里拼接一下数据 走离开房间流程，其实是踢出房间
                    var noticeLeaveInvite1 = { 'userId': inviteKickOut.userId, 'isCreator': false };
                    this.hallPageController.leaveRoom(noticeLeaveInvite1);
                }
                break;
            case MessageId_1.MessageId.MsgTypeUserInfo: //更新用户信息的消息
                var userInfo = messageBean.data;
                GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo = userInfo;
                this.hallPageController.updateGold();
                break;
            case MessageId_1.MessageId.MsgTypeSettlement: //大结算
                var noticeSettlement = messageBean.data;
                this.gamePageController.setCongratsDialog(noticeSettlement);
                break;
            case MessageId_1.MessageId.MsgTypeNoticeRoundStart: //扫雷回合开始通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeRoundStart(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeNoticeActionDisplay: //扫雷操作展示通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeActionDisplay(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeNoticeRoundEnd: //扫雷回合结束通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeRoundEnd(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeNoticeFirstChoiceBonus: //扫雷首选玩家奖励通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeFirstChoiceBonus(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeAIStatusChange: //AI托管状态变更通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onAIStatusChange(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeExtendLevelProgress: //关卡进度
                if (this.currentPage === PageType.HALL_PAGE) {
                    // 从后端获取关卡进度并更新
                    var levelProgressData = messageBean.data;
                    if (levelProgressData) {
                        this.hallPageController.setLevelProgress(levelProgressData);
                    }
                }
                break;
            case MessageId_1.MessageId.MsgTypeExtendLevelInfo: //关卡信息
                if (this.currentPage === PageType.LEVEL_PAGE) {
                    // 从后端获取关卡信息并更新
                    var levelInfoData = messageBean.data;
                    if (levelInfoData) {
                        this.levelPageController.onExtendLevelInfo(levelInfoData);
                    }
                }
                break;
            case MessageId_1.MessageId.MsgTypeDebugShowMines: //调试显示地雷位置
                if (this.currentPage === PageType.LEVEL_PAGE) {
                    // 从后端获取地雷位置数据并显示测试预制体
                    var minePositions = messageBean.data;
                    if (minePositions && Array.isArray(minePositions)) {
                        this.levelPageController.handleDebugShowMines(minePositions);
                    }
                }
                break;
        }
    };
    //设置展示页面的
    GlobalManagerController.prototype.setCurrentPage = function (pageType) {
        this.currentPage = pageType;
        this.startUpPage.active = false;
        this.hallPage.active = false;
        this.gamePage.active = false;
        this.levelPage.active = false;
        switch (pageType) {
            case PageType.START_UP_PAGE:
                this.startUpPage.active = true;
                break;
            case PageType.HALL_PAGE:
                this.hallPage.active = true;
                break;
            case PageType.GAME_PAGE:
                this.gamePage.active = true;
                break;
            case PageType.LEVEL_PAGE:
                this.levelPage.active = true;
                break;
        }
    };
    __decorate([
        property(TipsDialogController_1.default)
    ], GlobalManagerController.prototype, "tipsDialogController", void 0);
    __decorate([
        property(TopUpDialogController_1.default)
    ], GlobalManagerController.prototype, "topUpDialogController", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "netError", void 0);
    __decorate([
        property(ToastController_1.default)
    ], GlobalManagerController.prototype, "toastController", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "startUpPage", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "hallPage", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "gamePage", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "levelPage", void 0);
    GlobalManagerController = __decorate([
        ccclass
    ], GlobalManagerController);
    return GlobalManagerController;
}(cc.Component));
exports.default = GlobalManagerController;
if (!CC_EDITOR) {
    cc.Sprite.prototype["onLoad"] = function () {
        var _this = this;
        this.srcBlendFactor = cc.macro.BlendFactor.ONE;
        this.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
        // 延迟检查 spriteFrame，避免初始化时的警告
        this.scheduleOnce(function () {
            if (_this.spriteFrame && _this.spriteFrame.getTexture()) {
                _this.spriteFrame.getTexture().setPremultiplyAlpha(true);
            }
            // 移除警告，因为很多 Sprite 组件在初始化时确实没有 SpriteFrame
            // 这是正常的，不需要警告
        }, 0.1);
    };
    cc.Label.prototype["onLoad"] = function () {
        this.srcBlendFactor = cc.macro.BlendFactor.ONE;
        this.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
    };
    cc.macro.ALLOW_IMAGE_BITMAP = false; // 禁用 Bitmap 图片格式
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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