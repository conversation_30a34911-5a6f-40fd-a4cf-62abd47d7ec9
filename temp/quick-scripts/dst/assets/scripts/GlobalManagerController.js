
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/GlobalManagerController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '00371cQlglDVIdse39v0U2E', 'GlobalManagerController');
// scripts/GlobalManagerController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PageType = void 0;
var MeshTools_1 = require("../meshTools/MeshTools");
var Publish_1 = require("../meshTools/tools/Publish");
var EnumBean_1 = require("./bean/EnumBean");
var GlobalBean_1 = require("./bean/GlobalBean");
var LanguageType_1 = require("./bean/LanguageType");
var EventCenter_1 = require("./common/EventCenter");
var GameMgr_1 = require("./common/GameMgr");
var GamePageController_1 = require("./game/GamePageController");
var HallPageController_1 = require("./hall/HallPageController");
var LevelPageController_1 = require("./level/LevelPageController");
var TopUpDialogController_1 = require("./hall/TopUpDialogController");
var ErrorCode_1 = require("./net/ErrorCode");
var GameServerUrl_1 = require("./net/GameServerUrl");
var MessageBaseBean_1 = require("./net/MessageBaseBean");
var MessageId_1 = require("./net/MessageId");
var WebSocketManager_1 = require("./net/WebSocketManager");
var WebSocketTool_1 = require("./net/WebSocketTool");
var StartUpPageController_1 = require("./start_up/StartUpPageController");
var TipsDialogController_1 = require("./TipsDialogController");
var ToastController_1 = require("./ToastController");
var AudioMgr_1 = require("./util/AudioMgr");
var Config_1 = require("./util/Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
window.languageName = "en";
var PageType;
(function (PageType) {
    PageType[PageType["START_UP_PAGE"] = 0] = "START_UP_PAGE";
    PageType[PageType["HALL_PAGE"] = 1] = "HALL_PAGE";
    PageType[PageType["GAME_PAGE"] = 2] = "GAME_PAGE";
    PageType[PageType["LEVEL_PAGE"] = 3] = "LEVEL_PAGE";
})(PageType = exports.PageType || (exports.PageType = {}));
var GlobalManagerController = /** @class */ (function (_super) {
    __extends(GlobalManagerController, _super);
    function GlobalManagerController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.tipsDialogController = null; //这个是错误弹窗 只有一个退出按钮
        _this.topUpDialogController = null; //充值弹窗
        _this.netError = null; //这个是断网的时候展示的转圈的
        _this.toastController = null; //toast 的布局
        _this.startUpPage = null; //启动页
        _this.hallPage = null; //大厅页
        _this.gamePage = null; //游戏页面
        _this.levelPage = null; //关卡页面
        _this.currentPage = PageType.START_UP_PAGE; //当前展示的页面，默认展示的是启动页面
        _this.startUpPageController = null; //启动页面的总管理器
        _this.hallPageController = null; //大厅页面的总管理器
        _this.gamePageController = null; //游戏页面的总管理器
        _this.levelPageController = null; //关卡页面的总管理器
        return _this;
        // update (dt) {}
    }
    GlobalManagerController.prototype.onLoad = function () {
        cc.resources.preloadDir(Config_1.Config.hallRes, cc.SpriteFrame); //提前预加载大厅图片资源
        // 获取音频管理器实例
        var audioMgr = AudioMgr_1.AudioMgr.ins;
        // 初始化音频管理器（如果还未初始化）
        audioMgr.init();
        cc.debug.setDisplayStats(false);
        //获取URL拼接渠道参数
        this.getUrlParams();
        GameMgr_1.GameMgr.H5SDK.AddAPPEvent();
        this.getAppConfig();
        cc.game.on(cc.game.EVENT_SHOW, function () {
            GameMgr_1.GameMgr.Console.Log("EVENT_SHOW");
            GameMgr_1.GameMgr.GameData.GameIsInFront = true;
            // 触发重连
            WebSocketTool_1.WebSocketTool.GetInstance().atOnceReconnect();
        }, this);
        cc.game.on(cc.game.EVENT_HIDE, function () {
            GameMgr_1.GameMgr.Console.Log("EVENT_HIDE");
            GameMgr_1.GameMgr.GameData.GameIsInFront = false;
            // 断开WebSocket连接
            WebSocketTool_1.WebSocketTool.GetInstance().disconnect();
        }, this);
        //这里监听程序内消息
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.AutoMessage, this.onAutoMessage, this);
        //这里监听长链接消息（异常）
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveErrorMessage, this.onErrorMessage, this);
        //这里监听长链接消息（正常）
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveMessage, this.onMessage, this);
        this.setCurrentPage(PageType.START_UP_PAGE);
        this.startUpPageController = this.startUpPage.getComponent(StartUpPageController_1.default);
        this.hallPageController = this.hallPage.getComponent(HallPageController_1.default);
        this.gamePageController = this.gamePage.getComponent(GamePageController_1.default);
        this.levelPageController = this.levelPage.getComponent(LevelPageController_1.default);
    };
    GlobalManagerController.prototype.onEnable = function () {
    };
    GlobalManagerController.prototype.onDestroy = function () {
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.AutoMessage, this.onAutoMessage, this);
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveErrorMessage, this.onErrorMessage, this);
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveMessage, this.onMessage, this);
    };
    GlobalManagerController.prototype.getAppConfig = function () {
        var _this = this;
        GameMgr_1.GameMgr.H5SDK.GetConfig(function (config) {
            var _a, _b, _c;
            MeshTools_1.MeshTools.Publish.appChannel = String(config.appChannel);
            MeshTools_1.MeshTools.Publish.appId = parseInt(config.appId);
            MeshTools_1.MeshTools.Publish.gameMode = String(config.gameMode);
            MeshTools_1.MeshTools.Publish.roomId = (_a = String(config.roomId)) !== null && _a !== void 0 ? _a : "";
            MeshTools_1.MeshTools.Publish.currencyIcon = (_c = (_b = config === null || config === void 0 ? void 0 : config.gameConfig) === null || _b === void 0 ? void 0 : _b.currencyIcon) !== null && _c !== void 0 ? _c : "";
            MeshTools_1.MeshTools.Publish.code = encodeURIComponent(config.code);
            MeshTools_1.MeshTools.Publish.userId = String(config.userId);
            MeshTools_1.MeshTools.Publish.language = String(config.language);
            MeshTools_1.MeshTools.Publish.gsp = config.gsp == undefined ? 101 : parseInt(config.gsp);
            _this.getHpptPath();
        });
    };
    GlobalManagerController.prototype.getHpptPath = function () {
        this.setLanguage(); //先设置语言
        if (!window.navigator.onLine) {
            this.showTips(window.getLocalizedStr('NetworkError'));
        }
        else {
            // // 获取游戏服务器地址
            // HttpManager.Instance.ReqServerUrl(() => {
            //     let httpUrl: string = GameServerUrl.Http;
            //     let wsUrl: string = GameServerUrl.Ws;
            //     if (httpUrl != "" || wsUrl != "") {
            //         WebSocketManager.GetInstance().connect();
            //     }
            // });
            GameServerUrl_1.GameServerUrl.Ws = "ws://************:2059/acceptor";
            WebSocketManager_1.WebSocketManager.GetInstance().connect();
        }
    };
    GlobalManagerController.prototype.getUrlParams = function () {
        var params = GameMgr_1.GameMgr.Utils.GetUrlParams(window.location.href); //获取当前页面的 url
        if (JSON.stringify(params) != "{}") {
            //@ts-ignore
            if (params.appChannel) {
                //@ts-ignore
                MeshTools_1.MeshTools.Publish.appChannel = params.appChannel;
                if (params.isDataByUrl) {
                    if (params.isDataByUrl === "true") {
                        MeshTools_1.MeshTools.Publish.appId = parseInt(params.appId);
                        MeshTools_1.MeshTools.Publish.gameMode = params.gameMode;
                        MeshTools_1.MeshTools.Publish.userId = params.userId;
                        MeshTools_1.MeshTools.Publish.code = params.code;
                        if (params.language) {
                            MeshTools_1.MeshTools.Publish.language = params.language;
                        }
                        if (params.roomId) {
                            MeshTools_1.MeshTools.Publish.roomId = params.roomId;
                        }
                        if (params.gsp) {
                            MeshTools_1.MeshTools.Publish.gsp = parseInt(params.gsp);
                        }
                        MeshTools_1.MeshTools.Publish.isDataByURL = true;
                    }
                }
            }
        }
    };
    GlobalManagerController.prototype.setLanguage = function () {
        switch (Publish_1.Publish.GetInstance().language) {
            case LanguageType_1.default.SimplifiedChinese: //简体中文
                window.languageName = LanguageType_1.default.SimplifiedChinese_type;
                break;
            case LanguageType_1.default.TraditionalChinese: //繁体中文
                window.languageName = LanguageType_1.default.TraditionalChinese_type;
                break;
            default: //默认是英语
                window.languageName = LanguageType_1.default.English_type;
                break;
        }
        window.refreshAllLocalizedComp();
    };
    GlobalManagerController.prototype.showTips = function (content) {
        this.tipsDialogController.showDialog(content, function () {
            GameMgr_1.GameMgr.H5SDK.CloseWebView();
        });
    };
    //程序内的通知消息
    GlobalManagerController.prototype.onAutoMessage = function (autoMessageBean) {
        switch (autoMessageBean.msgId) {
            case MessageBaseBean_1.AutoMessageId.JumpHallPage: //跳转进大厅页面
                this.setCurrentPage(PageType.HALL_PAGE);
                if (autoMessageBean.data.type === 1) { //1是启动页面跳转的 ，2 是玩家主动离开游戏房间
                    this.hallPageController.LoginSuccess(); //因为初始进来的时候是启动页面，大厅页面是隐藏状态，下面发送的消息收不到，所以需要主动调用一次
                }
                else if (autoMessageBean.data.type === 2) { //2 是玩家主动离开游戏房间，需要更新关卡进度
                    // 单机模式退出关卡后，请求ExtendLevelProgress更新关卡信息
                    WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelProgress, {});
                }
                break;
            case MessageBaseBean_1.AutoMessageId.ReconnectionFailureMsg: //长链接重连失败
                this.netError.active = false;
                this.showTips(window.getLocalizedStr('NetworkError'));
                break;
            case MessageBaseBean_1.AutoMessageId.LinkExceptionMsg: //长链接异常
                this.netError.active = true;
                break;
            case MessageBaseBean_1.AutoMessageId.GameRouteNotFoundMsg: //游戏线路异常的通知
                this.showTips(autoMessageBean.data.code);
                break;
            case MessageBaseBean_1.AutoMessageId.SwitchGameSceneMsg: //切换游戏场景
                this.setCurrentPage(PageType.GAME_PAGE);
                break;
            case MessageBaseBean_1.AutoMessageId.WalletUpdateMsg: //更新金豆余额的通知
                //发送获取更新用户信息的消息
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeUserInfo, {});
                break;
            case MessageBaseBean_1.AutoMessageId.ServerCodeUpdateMsg: //更新 code 的通知
                Publish_1.Publish.GetInstance().code = autoMessageBean.data.code;
                break;
        }
    };
    //长链接消息(异常)
    GlobalManagerController.prototype.onErrorMessage = function (messageBean) {
        switch (messageBean.code) {
            case ErrorCode_1.ErrorCode.ErrInvalidInviteCode: //无效的邀请码
                this.hallPageController.joinError();
                break;
            case ErrorCode_1.ErrorCode.ErrRequestUser: //获取用户信息失败
                this.showTips(window.getLocalizedStr('GetUserInfoFailed'));
                // 断开WebSocket连接
                WebSocketTool_1.WebSocketTool.GetInstance().disconnect();
                break;
            case ErrorCode_1.ErrorCode.ErrNotFoundRoom: //没有找到指定的房间
                if (messageBean.msgId != MessageId_1.MessageId.MsgTypeMoveBlock) {
                    this.toastController.showContent(window.getLocalizedStr('RoomDoesNotExist'));
                    //没有找到房间 就直接返回到大厅页面
                    this.setCurrentPage(PageType.HALL_PAGE);
                }
                break;
            case ErrorCode_1.ErrorCode.ErrNotFoundUser: // 没有找到玩家信息
                if (messageBean.msgId === MessageId_1.MessageId.MsgTypeEnterRoom) { //只有在这个messageId下 才会踢出到大厅
                    //没有找到玩家信息 就直接返回到大厅页面
                    this.setCurrentPage(PageType.HALL_PAGE);
                }
                break;
            case ErrorCode_1.ErrorCode.ErrEnoughUser: //房间已满
                this.toastController.showContent(window.getLocalizedStr('RoomIsFull'));
                break;
            case ErrorCode_1.ErrorCode.ErrChangeBalance: //扣除金币失败
            case ErrorCode_1.ErrorCode.ErrNotEnoughCoin: //金币不足
                this.topUpDialogController.show(function () { });
                break;
            case ErrorCode_1.ErrorCode.ErrPlaying: //玩家已经在游戏中了
                //执行一遍 enterroom
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeEnterRoom, {}); //重连进来的 玩家请求进入房间
                break;
        }
    };
    //长链接消息(正常)
    GlobalManagerController.prototype.onMessage = function (messageBean) {
        switch (messageBean.msgId) {
            case MessageId_1.MessageId.MsgTypeCreateWs: //创建ws连接 成功  
                this.netError.active = false;
                //登录
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLogin, {});
                break;
            case MessageId_1.MessageId.MsgTypeLogin: //获取登录数据并存储
                GlobalBean_1.GlobalBean.GetInstance().loginData = messageBean.data;
                if (this.currentPage === PageType.START_UP_PAGE) {
                    //判断当前是否是在启动页面
                    this.startUpPageController.setLogin();
                }
                else {
                    this.hallPageController.updateGold();
                    this.hallPageController.LoginSuccess();
                    //没有在游戏中，但是还停留在游戏页面
                    if (GlobalBean_1.GlobalBean.GetInstance().loginData.roomId === 0 && this.currentPage === PageType.GAME_PAGE) {
                        this.setCurrentPage(PageType.HALL_PAGE); //返回到大厅
                    }
                }
                break;
            case MessageId_1.MessageId.MsgTypePairRequest: //开始匹配
                this.hallPageController.setHallOrMatch(HallPageController_1.HallOrMatch.MATCH_PARENT);
                this.hallPageController.createMatchView();
                break;
            case MessageId_1.MessageId.MsgTypeCancelPair: //取消匹配
                this.hallPageController.setHallOrMatch(HallPageController_1.HallOrMatch.HALL_PARENT);
                break;
            case MessageId_1.MessageId.MsgTypeGameStart: //游戏开始
                var noticeStartGame = messageBean.data;
                GlobalBean_1.GlobalBean.GetInstance().noticeStartGame = noticeStartGame; //存储游戏数据
                var index = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users.findIndex(function (item) { return item.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId; }); //搜索
                //把游戏开始之后最新的金币余额进行赋值
                if (index != -1) {
                    GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.coin = noticeStartGame.users[index].coin;
                }
                // 处理游戏开始数据，获取炸弹数量和地图类型
                this.gamePageController.onGameStart(noticeStartGame);
                if (noticeStartGame.roomType === EnumBean_1.RoomType.RoomTypeCommon) { // 房间类型 1-普通场 2-私人场
                    this.hallPageController.setGameData();
                }
                else {
                    this.setCurrentPage(PageType.GAME_PAGE); //开始游戏进入游戏页面
                }
                break;
            case MessageId_1.MessageId.MsgTypeEnterRoom: //重连的游戏数据
                var noticeStartGame2 = messageBean.data;
                GlobalBean_1.GlobalBean.GetInstance().noticeStartGame = noticeStartGame2; //存储游戏数据
                // 处理重连游戏数据，获取炸弹数量和地图类型
                this.gamePageController.onGameStart(noticeStartGame2);
                //跳转进游戏页面
                this.setCurrentPage(PageType.GAME_PAGE);
                break;
            case MessageId_1.MessageId.MsgTypeLeaveRoom: // 玩家主动离开房间
                var leaveRoomData = messageBean.data;
                // 检查是否是关卡游戏的LeaveRoom响应（包含levelId字段）
                if (leaveRoomData.levelId !== undefined) {
                    // 关卡游戏的LeaveRoom响应，直接返回大厅
                    cc.log("收到关卡游戏退出响应，返回大厅页面");
                    GlobalBean_1.GlobalBean.GetInstance().cleanData(); //清空数据
                    var autoMessageBean = {
                        'msgId': MessageBaseBean_1.AutoMessageId.JumpHallPage,
                        'data': { 'type': 2 } //2 是玩家主动离开游戏房间
                    };
                    GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
                }
                else if (leaveRoomData.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId) {
                    // 普通房间游戏的LeaveRoom响应
                    GlobalBean_1.GlobalBean.GetInstance().cleanData(); //清空数据
                    var autoMessageBean = {
                        'msgId': MessageBaseBean_1.AutoMessageId.JumpHallPage,
                        'data': { 'type': 2 } //2 是玩家主动离开游戏房间
                    };
                    GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
                }
                break;
            case MessageId_1.MessageId.MsgTypeCreateInvite: //创建邀请（也就是创建私人游戏房间）
                GlobalBean_1.GlobalBean.GetInstance().inviteInfo = messageBean.data;
                //点击 create 的回调
                this.hallPageController.joinCreateRoom();
                break;
            case MessageId_1.MessageId.MsgTypeAcceptInvite: //接受邀请
                var acceptInvite = messageBean.data;
                GlobalBean_1.GlobalBean.GetInstance().inviteInfo = acceptInvite.inviteInfo;
                this.hallPageController.setAcceptInvite(acceptInvite);
                break;
            case MessageId_1.MessageId.MsgTypeLeaveInvite: //收到离开房间的信息
                var noticeLeaveInvite = messageBean.data;
                this.hallPageController.leaveRoom(noticeLeaveInvite);
                break;
            case MessageId_1.MessageId.MsgTypeInviteReady: //收到有玩家准备的消息
                var noticeUserInviteStatus = messageBean.data;
                this.hallPageController.setReadyState(noticeUserInviteStatus);
                break;
            case MessageId_1.MessageId.MsgTypeNoticeInviteStatus: //广播邀请状态
                var noticeUserInviteStatus2 = messageBean.data;
                this.hallPageController.setReadyState(noticeUserInviteStatus2);
                break;
            case MessageId_1.MessageId.MsgTypeInviteKickOut: //收到玩家被踢出的信息
                var inviteKickOut = messageBean.data;
                if (inviteKickOut.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId) {
                    this.toastController.showContent(window.getLocalizedStr('KickOut'));
                    //被踢的是自己的话 直接返回大厅
                    this.setCurrentPage(PageType.HALL_PAGE);
                }
                else {
                    //这里拼接一下数据 走离开房间流程，其实是踢出房间
                    var noticeLeaveInvite1 = { 'userId': inviteKickOut.userId, 'isCreator': false };
                    this.hallPageController.leaveRoom(noticeLeaveInvite1);
                }
                break;
            case MessageId_1.MessageId.MsgTypeUserInfo: //更新用户信息的消息
                var userInfo = messageBean.data;
                GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo = userInfo;
                this.hallPageController.updateGold();
                break;
            case MessageId_1.MessageId.MsgTypeSettlement: //大结算
                var noticeSettlement = messageBean.data;
                this.gamePageController.setCongratsDialog(noticeSettlement);
                break;
            case MessageId_1.MessageId.MsgTypeNoticeRoundStart: //扫雷回合开始通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeRoundStart(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeNoticeActionDisplay: //扫雷操作展示通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeActionDisplay(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeNoticeRoundEnd: //扫雷回合结束通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeRoundEnd(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeNoticeFirstChoiceBonus: //扫雷首选玩家奖励通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeFirstChoiceBonus(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeAIStatusChange: //AI托管状态变更通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onAIStatusChange(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeExtendLevelProgress: //关卡进度
                if (this.currentPage === PageType.HALL_PAGE) {
                    // 从后端获取关卡进度并更新
                    var levelProgressData = messageBean.data;
                    if (levelProgressData) {
                        this.hallPageController.setLevelProgress(levelProgressData);
                    }
                }
                break;
            case MessageId_1.MessageId.MsgTypeExtendLevelInfo: //关卡信息
                if (this.currentPage === PageType.LEVEL_PAGE) {
                    // 从后端获取关卡信息并更新
                    var levelInfoData = messageBean.data;
                    if (levelInfoData) {
                        this.levelPageController.onExtendLevelInfo(levelInfoData);
                    }
                }
                break;
            case MessageId_1.MessageId.MsgTypeDebugShowMines: //调试显示地雷位置
                cc.log("=== 收到 DebugShowMines 消息 ===");
                cc.log("当前页面:", this.currentPage);
                cc.log("是否为关卡页面:", this.currentPage === PageType.LEVEL_PAGE);
                cc.log("消息数据:", messageBean.data);
                cc.log("levelPageController 是否存在:", !!this.levelPageController);
                if (this.currentPage === PageType.LEVEL_PAGE) {
                    // 从后端获取地雷位置数据并显示测试预制体
                    var minePositions = messageBean.data;
                    cc.log("地雷位置数据:", minePositions);
                    cc.log("是否为数组:", Array.isArray(minePositions));
                    if (minePositions && Array.isArray(minePositions)) {
                        cc.log("调用 handleDebugShowMines 方法");
                        this.levelPageController.handleDebugShowMines(minePositions);
                    }
                    else {
                        cc.warn("地雷位置数据格式不正确:", minePositions);
                    }
                }
                else {
                    cc.warn("当前不在关卡页面，无法处理 DebugShowMines 消息");
                }
                break;
        }
    };
    //设置展示页面的
    GlobalManagerController.prototype.setCurrentPage = function (pageType) {
        this.currentPage = pageType;
        this.startUpPage.active = false;
        this.hallPage.active = false;
        this.gamePage.active = false;
        this.levelPage.active = false;
        switch (pageType) {
            case PageType.START_UP_PAGE:
                this.startUpPage.active = true;
                break;
            case PageType.HALL_PAGE:
                this.hallPage.active = true;
                break;
            case PageType.GAME_PAGE:
                this.gamePage.active = true;
                break;
            case PageType.LEVEL_PAGE:
                this.levelPage.active = true;
                break;
        }
    };
    __decorate([
        property(TipsDialogController_1.default)
    ], GlobalManagerController.prototype, "tipsDialogController", void 0);
    __decorate([
        property(TopUpDialogController_1.default)
    ], GlobalManagerController.prototype, "topUpDialogController", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "netError", void 0);
    __decorate([
        property(ToastController_1.default)
    ], GlobalManagerController.prototype, "toastController", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "startUpPage", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "hallPage", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "gamePage", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "levelPage", void 0);
    GlobalManagerController = __decorate([
        ccclass
    ], GlobalManagerController);
    return GlobalManagerController;
}(cc.Component));
exports.default = GlobalManagerController;
if (!CC_EDITOR) {
    cc.Sprite.prototype["onLoad"] = function () {
        var _this = this;
        this.srcBlendFactor = cc.macro.BlendFactor.ONE;
        this.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
        // 延迟检查 spriteFrame，避免初始化时的警告
        this.scheduleOnce(function () {
            if (_this.spriteFrame && _this.spriteFrame.getTexture()) {
                _this.spriteFrame.getTexture().setPremultiplyAlpha(true);
            }
            // 移除警告，因为很多 Sprite 组件在初始化时确实没有 SpriteFrame
            // 这是正常的，不需要警告
        }, 0.1);
    };
    cc.Label.prototype["onLoad"] = function () {
        this.srcBlendFactor = cc.macro.BlendFactor.ONE;
        this.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
    };
    cc.macro.ALLOW_IMAGE_BITMAP = false; // 禁用 Bitmap 图片格式
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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