
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/GlobalManagerController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '00371cQlglDVIdse39v0U2E', 'GlobalManagerController');
// scripts/GlobalManagerController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PageType = void 0;
var MeshTools_1 = require("../meshTools/MeshTools");
var Publish_1 = require("../meshTools/tools/Publish");
var EnumBean_1 = require("./bean/EnumBean");
var GlobalBean_1 = require("./bean/GlobalBean");
var LanguageType_1 = require("./bean/LanguageType");
var EventCenter_1 = require("./common/EventCenter");
var GameMgr_1 = require("./common/GameMgr");
var GamePageController_1 = require("./game/GamePageController");
var HallPageController_1 = require("./hall/HallPageController");
var LevelPageController_1 = require("./level/LevelPageController");
var TopUpDialogController_1 = require("./hall/TopUpDialogController");
var ErrorCode_1 = require("./net/ErrorCode");
var GameServerUrl_1 = require("./net/GameServerUrl");
var MessageBaseBean_1 = require("./net/MessageBaseBean");
var MessageId_1 = require("./net/MessageId");
var WebSocketManager_1 = require("./net/WebSocketManager");
var WebSocketTool_1 = require("./net/WebSocketTool");
var StartUpPageController_1 = require("./start_up/StartUpPageController");
var TipsDialogController_1 = require("./TipsDialogController");
var ToastController_1 = require("./ToastController");
var AudioMgr_1 = require("./util/AudioMgr");
var Config_1 = require("./util/Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
window.languageName = "en";
var PageType;
(function (PageType) {
    PageType[PageType["START_UP_PAGE"] = 0] = "START_UP_PAGE";
    PageType[PageType["HALL_PAGE"] = 1] = "HALL_PAGE";
    PageType[PageType["GAME_PAGE"] = 2] = "GAME_PAGE";
    PageType[PageType["LEVEL_PAGE"] = 3] = "LEVEL_PAGE";
})(PageType = exports.PageType || (exports.PageType = {}));
var GlobalManagerController = /** @class */ (function (_super) {
    __extends(GlobalManagerController, _super);
    function GlobalManagerController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.tipsDialogController = null; //这个是错误弹窗 只有一个退出按钮
        _this.topUpDialogController = null; //充值弹窗
        _this.netError = null; //这个是断网的时候展示的转圈的
        _this.toastController = null; //toast 的布局
        _this.startUpPage = null; //启动页
        _this.hallPage = null; //大厅页
        _this.gamePage = null; //游戏页面
        _this.levelPage = null; //关卡页面
        _this.currentPage = PageType.START_UP_PAGE; //当前展示的页面，默认展示的是启动页面
        _this.startUpPageController = null; //启动页面的总管理器
        _this.hallPageController = null; //大厅页面的总管理器
        _this.gamePageController = null; //游戏页面的总管理器
        _this.levelPageController = null; //关卡页面的总管理器
        return _this;
        // update (dt) {}
    }
    GlobalManagerController.prototype.onLoad = function () {
        cc.resources.preloadDir(Config_1.Config.hallRes, cc.SpriteFrame); //提前预加载大厅图片资源
        // 获取音频管理器实例
        var audioMgr = AudioMgr_1.AudioMgr.ins;
        // 初始化音频管理器（如果还未初始化）
        audioMgr.init();
        cc.debug.setDisplayStats(false);
        //获取URL拼接渠道参数
        this.getUrlParams();
        GameMgr_1.GameMgr.H5SDK.AddAPPEvent();
        this.getAppConfig();
        cc.game.on(cc.game.EVENT_SHOW, function () {
            GameMgr_1.GameMgr.Console.Log("EVENT_SHOW");
            GameMgr_1.GameMgr.GameData.GameIsInFront = true;
            // 触发重连
            WebSocketTool_1.WebSocketTool.GetInstance().atOnceReconnect();
        }, this);
        cc.game.on(cc.game.EVENT_HIDE, function () {
            GameMgr_1.GameMgr.Console.Log("EVENT_HIDE");
            GameMgr_1.GameMgr.GameData.GameIsInFront = false;
            // 断开WebSocket连接
            WebSocketTool_1.WebSocketTool.GetInstance().disconnect();
        }, this);
        //这里监听程序内消息
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.AutoMessage, this.onAutoMessage, this);
        //这里监听长链接消息（异常）
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveErrorMessage, this.onErrorMessage, this);
        //这里监听长链接消息（正常）
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveMessage, this.onMessage, this);
        this.setCurrentPage(PageType.START_UP_PAGE);
        this.startUpPageController = this.startUpPage.getComponent(StartUpPageController_1.default);
        this.hallPageController = this.hallPage.getComponent(HallPageController_1.default);
        this.gamePageController = this.gamePage.getComponent(GamePageController_1.default);
        this.levelPageController = this.levelPage.getComponent(LevelPageController_1.default);
    };
    GlobalManagerController.prototype.onEnable = function () {
    };
    GlobalManagerController.prototype.onDestroy = function () {
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.AutoMessage, this.onAutoMessage, this);
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveErrorMessage, this.onErrorMessage, this);
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveMessage, this.onMessage, this);
    };
    GlobalManagerController.prototype.getAppConfig = function () {
        var _this = this;
        GameMgr_1.GameMgr.H5SDK.GetConfig(function (config) {
            var _a, _b, _c;
            MeshTools_1.MeshTools.Publish.appChannel = String(config.appChannel);
            MeshTools_1.MeshTools.Publish.appId = parseInt(config.appId);
            MeshTools_1.MeshTools.Publish.gameMode = String(config.gameMode);
            MeshTools_1.MeshTools.Publish.roomId = (_a = String(config.roomId)) !== null && _a !== void 0 ? _a : "";
            MeshTools_1.MeshTools.Publish.currencyIcon = (_c = (_b = config === null || config === void 0 ? void 0 : config.gameConfig) === null || _b === void 0 ? void 0 : _b.currencyIcon) !== null && _c !== void 0 ? _c : "";
            MeshTools_1.MeshTools.Publish.code = encodeURIComponent(config.code);
            MeshTools_1.MeshTools.Publish.userId = String(config.userId);
            MeshTools_1.MeshTools.Publish.language = String(config.language);
            MeshTools_1.MeshTools.Publish.gsp = config.gsp == undefined ? 101 : parseInt(config.gsp);
            _this.getHpptPath();
        });
    };
    GlobalManagerController.prototype.getHpptPath = function () {
        this.setLanguage(); //先设置语言
        if (!window.navigator.onLine) {
            this.showTips(window.getLocalizedStr('NetworkError'));
        }
        else {
            // // 获取游戏服务器地址
            // HttpManager.Instance.ReqServerUrl(() => {
            //     let httpUrl: string = GameServerUrl.Http;
            //     let wsUrl: string = GameServerUrl.Ws;
            //     if (httpUrl != "" || wsUrl != "") {
            //         WebSocketManager.GetInstance().connect();
            //     }
            // });
            GameServerUrl_1.GameServerUrl.Ws = "ws://************:2059/acceptor";
            WebSocketManager_1.WebSocketManager.GetInstance().connect();
        }
    };
    GlobalManagerController.prototype.getUrlParams = function () {
        var params = GameMgr_1.GameMgr.Utils.GetUrlParams(window.location.href); //获取当前页面的 url
        if (JSON.stringify(params) != "{}") {
            //@ts-ignore
            if (params.appChannel) {
                //@ts-ignore
                MeshTools_1.MeshTools.Publish.appChannel = params.appChannel;
                if (params.isDataByUrl) {
                    if (params.isDataByUrl === "true") {
                        MeshTools_1.MeshTools.Publish.appId = parseInt(params.appId);
                        MeshTools_1.MeshTools.Publish.gameMode = params.gameMode;
                        MeshTools_1.MeshTools.Publish.userId = params.userId;
                        MeshTools_1.MeshTools.Publish.code = params.code;
                        if (params.language) {
                            MeshTools_1.MeshTools.Publish.language = params.language;
                        }
                        if (params.roomId) {
                            MeshTools_1.MeshTools.Publish.roomId = params.roomId;
                        }
                        if (params.gsp) {
                            MeshTools_1.MeshTools.Publish.gsp = parseInt(params.gsp);
                        }
                        MeshTools_1.MeshTools.Publish.isDataByURL = true;
                    }
                }
            }
        }
    };
    GlobalManagerController.prototype.setLanguage = function () {
        switch (Publish_1.Publish.GetInstance().language) {
            case LanguageType_1.default.SimplifiedChinese: //简体中文
                window.languageName = LanguageType_1.default.SimplifiedChinese_type;
                break;
            case LanguageType_1.default.TraditionalChinese: //繁体中文
                window.languageName = LanguageType_1.default.TraditionalChinese_type;
                break;
            default: //默认是英语
                window.languageName = LanguageType_1.default.English_type;
                break;
        }
        window.refreshAllLocalizedComp();
    };
    GlobalManagerController.prototype.showTips = function (content) {
        this.tipsDialogController.showDialog(content, function () {
            GameMgr_1.GameMgr.H5SDK.CloseWebView();
        });
    };
    //程序内的通知消息
    GlobalManagerController.prototype.onAutoMessage = function (autoMessageBean) {
        switch (autoMessageBean.msgId) {
            case MessageBaseBean_1.AutoMessageId.JumpHallPage: //跳转进大厅页面
                this.setCurrentPage(PageType.HALL_PAGE);
                if (autoMessageBean.data.type === 1) { //1是启动页面跳转的 ，2 是玩家主动离开游戏房间
                    this.hallPageController.LoginSuccess(); //因为初始进来的时候是启动页面，大厅页面是隐藏状态，下面发送的消息收不到，所以需要主动调用一次
                }
                break;
            case MessageBaseBean_1.AutoMessageId.ReconnectionFailureMsg: //长链接重连失败
                this.netError.active = false;
                this.showTips(window.getLocalizedStr('NetworkError'));
                break;
            case MessageBaseBean_1.AutoMessageId.LinkExceptionMsg: //长链接异常
                this.netError.active = true;
                break;
            case MessageBaseBean_1.AutoMessageId.GameRouteNotFoundMsg: //游戏线路异常的通知
                this.showTips(autoMessageBean.data.code);
                break;
            case MessageBaseBean_1.AutoMessageId.SwitchGameSceneMsg: //切换游戏场景
                this.setCurrentPage(PageType.GAME_PAGE);
                break;
            case MessageBaseBean_1.AutoMessageId.WalletUpdateMsg: //更新金豆余额的通知
                //发送获取更新用户信息的消息
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeUserInfo, {});
                break;
            case MessageBaseBean_1.AutoMessageId.ServerCodeUpdateMsg: //更新 code 的通知
                Publish_1.Publish.GetInstance().code = autoMessageBean.data.code;
                break;
        }
    };
    //长链接消息(异常)
    GlobalManagerController.prototype.onErrorMessage = function (messageBean) {
        switch (messageBean.code) {
            case ErrorCode_1.ErrorCode.ErrInvalidInviteCode: //无效的邀请码
                this.hallPageController.joinError();
                break;
            case ErrorCode_1.ErrorCode.ErrRequestUser: //获取用户信息失败
                this.showTips(window.getLocalizedStr('GetUserInfoFailed'));
                // 断开WebSocket连接
                WebSocketTool_1.WebSocketTool.GetInstance().disconnect();
                break;
            case ErrorCode_1.ErrorCode.ErrNotFoundRoom: //没有找到指定的房间
                if (messageBean.msgId != MessageId_1.MessageId.MsgTypeMoveBlock) {
                    this.toastController.showContent(window.getLocalizedStr('RoomDoesNotExist'));
                    //没有找到房间 就直接返回到大厅页面
                    this.setCurrentPage(PageType.HALL_PAGE);
                }
                break;
            case ErrorCode_1.ErrorCode.ErrNotFoundUser: // 没有找到玩家信息
                if (messageBean.msgId === MessageId_1.MessageId.MsgTypeEnterRoom) { //只有在这个messageId下 才会踢出到大厅
                    //没有找到玩家信息 就直接返回到大厅页面
                    this.setCurrentPage(PageType.HALL_PAGE);
                }
                break;
            case ErrorCode_1.ErrorCode.ErrEnoughUser: //房间已满
                this.toastController.showContent(window.getLocalizedStr('RoomIsFull'));
                break;
            case ErrorCode_1.ErrorCode.ErrChangeBalance: //扣除金币失败
            case ErrorCode_1.ErrorCode.ErrNotEnoughCoin: //金币不足
                this.topUpDialogController.show(function () { });
                break;
            case ErrorCode_1.ErrorCode.ErrPlaying: //玩家已经在游戏中了
                //执行一遍 enterroom
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeEnterRoom, {}); //重连进来的 玩家请求进入房间
                break;
        }
    };
    //长链接消息(正常)
    GlobalManagerController.prototype.onMessage = function (messageBean) {
        switch (messageBean.msgId) {
            case MessageId_1.MessageId.MsgTypeCreateWs: //创建ws连接 成功  
                this.netError.active = false;
                //登录
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLogin, {});
                break;
            case MessageId_1.MessageId.MsgTypeLogin: //获取登录数据并存储
                GlobalBean_1.GlobalBean.GetInstance().loginData = messageBean.data;
                if (this.currentPage === PageType.START_UP_PAGE) {
                    //判断当前是否是在启动页面
                    this.startUpPageController.setLogin();
                }
                else {
                    this.hallPageController.updateGold();
                    this.hallPageController.LoginSuccess();
                    //没有在游戏中，但是还停留在游戏页面
                    if (GlobalBean_1.GlobalBean.GetInstance().loginData.roomId === 0 && this.currentPage === PageType.GAME_PAGE) {
                        this.setCurrentPage(PageType.HALL_PAGE); //返回到大厅
                    }
                }
                break;
            case MessageId_1.MessageId.MsgTypePairRequest: //开始匹配
                this.hallPageController.setHallOrMatch(HallPageController_1.HallOrMatch.MATCH_PARENT);
                this.hallPageController.createMatchView();
                break;
            case MessageId_1.MessageId.MsgTypeCancelPair: //取消匹配
                this.hallPageController.setHallOrMatch(HallPageController_1.HallOrMatch.HALL_PARENT);
                break;
            case MessageId_1.MessageId.MsgTypeGameStart: //游戏开始
                var noticeStartGame = messageBean.data;
                GlobalBean_1.GlobalBean.GetInstance().noticeStartGame = noticeStartGame; //存储游戏数据
                var index = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users.findIndex(function (item) { return item.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId; }); //搜索
                //把游戏开始之后最新的金币余额进行赋值
                if (index != -1) {
                    GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.coin = noticeStartGame.users[index].coin;
                }
                // 处理游戏开始数据，获取炸弹数量和地图类型
                this.gamePageController.onGameStart(noticeStartGame);
                if (noticeStartGame.roomType === EnumBean_1.RoomType.RoomTypeCommon) { // 房间类型 1-普通场 2-私人场
                    this.hallPageController.setGameData();
                }
                else {
                    this.setCurrentPage(PageType.GAME_PAGE); //开始游戏进入游戏页面
                }
                break;
            case MessageId_1.MessageId.MsgTypeEnterRoom: //重连的游戏数据
                var noticeStartGame2 = messageBean.data;
                GlobalBean_1.GlobalBean.GetInstance().noticeStartGame = noticeStartGame2; //存储游戏数据
                // 处理重连游戏数据，获取炸弹数量和地图类型
                this.gamePageController.onGameStart(noticeStartGame2);
                //跳转进游戏页面
                this.setCurrentPage(PageType.GAME_PAGE);
                break;
            case MessageId_1.MessageId.MsgTypeLeaveRoom: // 玩家主动离开房间
                var leaveRoomData = messageBean.data;
                // 检查是否是关卡游戏的LeaveRoom响应（包含levelId字段）
                if (leaveRoomData.levelId !== undefined) {
                    // 关卡游戏的LeaveRoom响应，直接返回大厅
                    cc.log("收到关卡游戏退出响应，返回大厅页面");
                    GlobalBean_1.GlobalBean.GetInstance().cleanData(); //清空数据
                    var autoMessageBean = {
                        'msgId': MessageBaseBean_1.AutoMessageId.JumpHallPage,
                        'data': { 'type': 2 } //2 是玩家主动离开游戏房间
                    };
                    GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
                }
                else if (leaveRoomData.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId) {
                    // 普通房间游戏的LeaveRoom响应
                    GlobalBean_1.GlobalBean.GetInstance().cleanData(); //清空数据
                    var autoMessageBean = {
                        'msgId': MessageBaseBean_1.AutoMessageId.JumpHallPage,
                        'data': { 'type': 2 } //2 是玩家主动离开游戏房间
                    };
                    GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
                }
                break;
            case MessageId_1.MessageId.MsgTypeCreateInvite: //创建邀请（也就是创建私人游戏房间）
                GlobalBean_1.GlobalBean.GetInstance().inviteInfo = messageBean.data;
                //点击 create 的回调
                this.hallPageController.joinCreateRoom();
                break;
            case MessageId_1.MessageId.MsgTypeAcceptInvite: //接受邀请
                var acceptInvite = messageBean.data;
                GlobalBean_1.GlobalBean.GetInstance().inviteInfo = acceptInvite.inviteInfo;
                this.hallPageController.setAcceptInvite(acceptInvite);
                break;
            case MessageId_1.MessageId.MsgTypeLeaveInvite: //收到离开房间的信息
                var noticeLeaveInvite = messageBean.data;
                this.hallPageController.leaveRoom(noticeLeaveInvite);
                break;
            case MessageId_1.MessageId.MsgTypeInviteReady: //收到有玩家准备的消息
                var noticeUserInviteStatus = messageBean.data;
                this.hallPageController.setReadyState(noticeUserInviteStatus);
                break;
            case MessageId_1.MessageId.MsgTypeNoticeInviteStatus: //广播邀请状态
                var noticeUserInviteStatus2 = messageBean.data;
                this.hallPageController.setReadyState(noticeUserInviteStatus2);
                break;
            case MessageId_1.MessageId.MsgTypeInviteKickOut: //收到玩家被踢出的信息
                var inviteKickOut = messageBean.data;
                if (inviteKickOut.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId) {
                    this.toastController.showContent(window.getLocalizedStr('KickOut'));
                    //被踢的是自己的话 直接返回大厅
                    this.setCurrentPage(PageType.HALL_PAGE);
                }
                else {
                    //这里拼接一下数据 走离开房间流程，其实是踢出房间
                    var noticeLeaveInvite1 = { 'userId': inviteKickOut.userId, 'isCreator': false };
                    this.hallPageController.leaveRoom(noticeLeaveInvite1);
                }
                break;
            case MessageId_1.MessageId.MsgTypeUserInfo: //更新用户信息的消息
                var userInfo = messageBean.data;
                GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo = userInfo;
                this.hallPageController.updateGold();
                break;
            case MessageId_1.MessageId.MsgTypeSettlement: //大结算
                var noticeSettlement = messageBean.data;
                this.gamePageController.setCongratsDialog(noticeSettlement);
                break;
            case MessageId_1.MessageId.MsgTypeNoticeRoundStart: //扫雷回合开始通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeRoundStart(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeNoticeActionDisplay: //扫雷操作展示通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeActionDisplay(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeNoticeRoundEnd: //扫雷回合结束通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeRoundEnd(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeNoticeFirstChoiceBonus: //扫雷首选玩家奖励通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeFirstChoiceBonus(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeAIStatusChange: //AI托管状态变更通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onAIStatusChange(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeExtendLevelProgress: //关卡进度
                if (this.currentPage === PageType.HALL_PAGE) {
                    // 从后端获取关卡进度并更新
                    var levelProgressData = messageBean.data;
                    if (levelProgressData) {
                        this.hallPageController.setLevelProgress(levelProgressData);
                    }
                }
                break;
            case MessageId_1.MessageId.MsgTypeExtendLevelInfo: //关卡信息
                if (this.currentPage === PageType.LEVEL_PAGE) {
                    // 从后端获取关卡信息并更新
                    var levelInfoData = messageBean.data;
                    if (levelInfoData) {
                        this.levelPageController.onExtendLevelInfo(levelInfoData);
                    }
                }
                break;
        }
    };
    //设置展示页面的
    GlobalManagerController.prototype.setCurrentPage = function (pageType) {
        this.currentPage = pageType;
        this.startUpPage.active = false;
        this.hallPage.active = false;
        this.gamePage.active = false;
        this.levelPage.active = false;
        switch (pageType) {
            case PageType.START_UP_PAGE:
                this.startUpPage.active = true;
                break;
            case PageType.HALL_PAGE:
                this.hallPage.active = true;
                break;
            case PageType.GAME_PAGE:
                this.gamePage.active = true;
                break;
            case PageType.LEVEL_PAGE:
                this.levelPage.active = true;
                break;
        }
    };
    __decorate([
        property(TipsDialogController_1.default)
    ], GlobalManagerController.prototype, "tipsDialogController", void 0);
    __decorate([
        property(TopUpDialogController_1.default)
    ], GlobalManagerController.prototype, "topUpDialogController", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "netError", void 0);
    __decorate([
        property(ToastController_1.default)
    ], GlobalManagerController.prototype, "toastController", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "startUpPage", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "hallPage", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "gamePage", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "levelPage", void 0);
    GlobalManagerController = __decorate([
        ccclass
    ], GlobalManagerController);
    return GlobalManagerController;
}(cc.Component));
exports.default = GlobalManagerController;
if (!CC_EDITOR) {
    cc.Sprite.prototype["onLoad"] = function () {
        var _this = this;
        this.srcBlendFactor = cc.macro.BlendFactor.ONE;
        this.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
        // 延迟检查 spriteFrame，避免初始化时的警告
        this.scheduleOnce(function () {
            if (_this.spriteFrame && _this.spriteFrame.getTexture()) {
                _this.spriteFrame.getTexture().setPremultiplyAlpha(true);
            }
            // 移除警告，因为很多 Sprite 组件在初始化时确实没有 SpriteFrame
            // 这是正常的，不需要警告
        }, 0.1);
    };
    cc.Label.prototype["onLoad"] = function () {
        this.srcBlendFactor = cc.macro.BlendFactor.ONE;
        this.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
    };
    cc.macro.ALLOW_IMAGE_BITMAP = false; // 禁用 Bitmap 图片格式
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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