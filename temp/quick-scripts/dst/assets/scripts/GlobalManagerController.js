
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/GlobalManagerController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '00371cQlglDVIdse39v0U2E', 'GlobalManagerController');
// scripts/GlobalManagerController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PageType = void 0;
var MeshTools_1 = require("../meshTools/MeshTools");
var Publish_1 = require("../meshTools/tools/Publish");
var EnumBean_1 = require("./bean/EnumBean");
var GlobalBean_1 = require("./bean/GlobalBean");
var LanguageType_1 = require("./bean/LanguageType");
var EventCenter_1 = require("./common/EventCenter");
var GameMgr_1 = require("./common/GameMgr");
var GamePageController_1 = require("./game/GamePageController");
var HallPageController_1 = require("./hall/HallPageController");
var LevelPageController_1 = require("./level/LevelPageController");
var TopUpDialogController_1 = require("./hall/TopUpDialogController");
var ErrorCode_1 = require("./net/ErrorCode");
var GameServerUrl_1 = require("./net/GameServerUrl");
var MessageBaseBean_1 = require("./net/MessageBaseBean");
var MessageId_1 = require("./net/MessageId");
var WebSocketManager_1 = require("./net/WebSocketManager");
var WebSocketTool_1 = require("./net/WebSocketTool");
var StartUpPageController_1 = require("./start_up/StartUpPageController");
var TipsDialogController_1 = require("./TipsDialogController");
var ToastController_1 = require("./ToastController");
var AudioMgr_1 = require("./util/AudioMgr");
var Config_1 = require("./util/Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
window.languageName = "en";
var PageType;
(function (PageType) {
    PageType[PageType["START_UP_PAGE"] = 0] = "START_UP_PAGE";
    PageType[PageType["HALL_PAGE"] = 1] = "HALL_PAGE";
    PageType[PageType["GAME_PAGE"] = 2] = "GAME_PAGE";
    PageType[PageType["LEVEL_PAGE"] = 3] = "LEVEL_PAGE";
})(PageType = exports.PageType || (exports.PageType = {}));
var GlobalManagerController = /** @class */ (function (_super) {
    __extends(GlobalManagerController, _super);
    function GlobalManagerController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.tipsDialogController = null; //这个是错误弹窗 只有一个退出按钮
        _this.topUpDialogController = null; //充值弹窗
        _this.netError = null; //这个是断网的时候展示的转圈的
        _this.toastController = null; //toast 的布局
        _this.startUpPage = null; //启动页
        _this.hallPage = null; //大厅页
        _this.gamePage = null; //游戏页面
        _this.levelPage = null; //关卡页面
        _this.currentPage = PageType.START_UP_PAGE; //当前展示的页面，默认展示的是启动页面
        _this.startUpPageController = null; //启动页面的总管理器
        _this.hallPageController = null; //大厅页面的总管理器
        _this.gamePageController = null; //游戏页面的总管理器
        _this.levelPageController = null; //关卡页面的总管理器
        return _this;
        // update (dt) {}
    }
    GlobalManagerController.prototype.onLoad = function () {
        cc.resources.preloadDir(Config_1.Config.hallRes, cc.SpriteFrame); //提前预加载大厅图片资源
        // 获取音频管理器实例
        var audioMgr = AudioMgr_1.AudioMgr.ins;
        // 初始化音频管理器（如果还未初始化）
        audioMgr.init();
        cc.debug.setDisplayStats(false);
        //获取URL拼接渠道参数
        this.getUrlParams();
        GameMgr_1.GameMgr.H5SDK.AddAPPEvent();
        this.getAppConfig();
        cc.game.on(cc.game.EVENT_SHOW, function () {
            GameMgr_1.GameMgr.Console.Log("EVENT_SHOW");
            GameMgr_1.GameMgr.GameData.GameIsInFront = true;
            // 触发重连
            WebSocketTool_1.WebSocketTool.GetInstance().atOnceReconnect();
        }, this);
        cc.game.on(cc.game.EVENT_HIDE, function () {
            GameMgr_1.GameMgr.Console.Log("EVENT_HIDE");
            GameMgr_1.GameMgr.GameData.GameIsInFront = false;
            // 断开WebSocket连接
            WebSocketTool_1.WebSocketTool.GetInstance().disconnect();
        }, this);
        //这里监听程序内消息
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.AutoMessage, this.onAutoMessage, this);
        //这里监听长链接消息（异常）
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveErrorMessage, this.onErrorMessage, this);
        //这里监听长链接消息（正常）
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveMessage, this.onMessage, this);
        this.setCurrentPage(PageType.START_UP_PAGE);
        this.startUpPageController = this.startUpPage.getComponent(StartUpPageController_1.default);
        this.hallPageController = this.hallPage.getComponent(HallPageController_1.default);
        this.gamePageController = this.gamePage.getComponent(GamePageController_1.default);
        this.levelPageController = this.levelPage.getComponent(LevelPageController_1.default);
    };
    GlobalManagerController.prototype.onEnable = function () {
    };
    GlobalManagerController.prototype.onDestroy = function () {
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.AutoMessage, this.onAutoMessage, this);
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveErrorMessage, this.onErrorMessage, this);
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveMessage, this.onMessage, this);
    };
    GlobalManagerController.prototype.getAppConfig = function () {
        var _this = this;
        GameMgr_1.GameMgr.H5SDK.GetConfig(function (config) {
            var _a, _b, _c;
            MeshTools_1.MeshTools.Publish.appChannel = String(config.appChannel);
            MeshTools_1.MeshTools.Publish.appId = parseInt(config.appId);
            MeshTools_1.MeshTools.Publish.gameMode = String(config.gameMode);
            MeshTools_1.MeshTools.Publish.roomId = (_a = String(config.roomId)) !== null && _a !== void 0 ? _a : "";
            MeshTools_1.MeshTools.Publish.currencyIcon = (_c = (_b = config === null || config === void 0 ? void 0 : config.gameConfig) === null || _b === void 0 ? void 0 : _b.currencyIcon) !== null && _c !== void 0 ? _c : "";
            MeshTools_1.MeshTools.Publish.code = encodeURIComponent(config.code);
            MeshTools_1.MeshTools.Publish.userId = String(config.userId);
            MeshTools_1.MeshTools.Publish.language = String(config.language);
            MeshTools_1.MeshTools.Publish.gsp = config.gsp == undefined ? 101 : parseInt(config.gsp);
            _this.getHpptPath();
        });
    };
    GlobalManagerController.prototype.getHpptPath = function () {
        this.setLanguage(); //先设置语言
        if (!window.navigator.onLine) {
            this.showTips(window.getLocalizedStr('NetworkError'));
        }
        else {
            // // 获取游戏服务器地址
            // HttpManager.Instance.ReqServerUrl(() => {
            //     let httpUrl: string = GameServerUrl.Http;
            //     let wsUrl: string = GameServerUrl.Ws;
            //     if (httpUrl != "" || wsUrl != "") {
            //         WebSocketManager.GetInstance().connect();
            //     }
            // });
            GameServerUrl_1.GameServerUrl.Ws = "ws://************:2059/acceptor";
            WebSocketManager_1.WebSocketManager.GetInstance().connect();
        }
    };
    GlobalManagerController.prototype.getUrlParams = function () {
        var params = GameMgr_1.GameMgr.Utils.GetUrlParams(window.location.href); //获取当前页面的 url
        if (JSON.stringify(params) != "{}") {
            //@ts-ignore
            if (params.appChannel) {
                //@ts-ignore
                MeshTools_1.MeshTools.Publish.appChannel = params.appChannel;
                if (params.isDataByUrl) {
                    if (params.isDataByUrl === "true") {
                        MeshTools_1.MeshTools.Publish.appId = parseInt(params.appId);
                        MeshTools_1.MeshTools.Publish.gameMode = params.gameMode;
                        MeshTools_1.MeshTools.Publish.userId = params.userId;
                        MeshTools_1.MeshTools.Publish.code = params.code;
                        if (params.language) {
                            MeshTools_1.MeshTools.Publish.language = params.language;
                        }
                        if (params.roomId) {
                            MeshTools_1.MeshTools.Publish.roomId = params.roomId;
                        }
                        if (params.gsp) {
                            MeshTools_1.MeshTools.Publish.gsp = parseInt(params.gsp);
                        }
                        MeshTools_1.MeshTools.Publish.isDataByURL = true;
                    }
                }
            }
        }
    };
    GlobalManagerController.prototype.setLanguage = function () {
        switch (Publish_1.Publish.GetInstance().language) {
            case LanguageType_1.default.SimplifiedChinese: //简体中文
                window.languageName = LanguageType_1.default.SimplifiedChinese_type;
                break;
            case LanguageType_1.default.TraditionalChinese: //繁体中文
                window.languageName = LanguageType_1.default.TraditionalChinese_type;
                break;
            default: //默认是英语
                window.languageName = LanguageType_1.default.English_type;
                break;
        }
        window.refreshAllLocalizedComp();
    };
    GlobalManagerController.prototype.showTips = function (content) {
        this.tipsDialogController.showDialog(content, function () {
            GameMgr_1.GameMgr.H5SDK.CloseWebView();
        });
    };
    //程序内的通知消息
    GlobalManagerController.prototype.onAutoMessage = function (autoMessageBean) {
        switch (autoMessageBean.msgId) {
            case MessageBaseBean_1.AutoMessageId.JumpHallPage: //跳转进大厅页面
                this.setCurrentPage(PageType.HALL_PAGE);
                if (autoMessageBean.data.type === 1) { //1是启动页面跳转的 ，2 是玩家主动离开游戏房间
                    this.hallPageController.LoginSuccess(); //因为初始进来的时候是启动页面，大厅页面是隐藏状态，下面发送的消息收不到，所以需要主动调用一次
                }
                else if (autoMessageBean.data.type === 2) { //2 是玩家主动离开游戏房间，需要更新关卡进度
                    // 单机模式退出关卡后，请求ExtendLevelProgress更新关卡信息
                    WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelProgress, {});
                }
                break;
            case MessageBaseBean_1.AutoMessageId.ReconnectionFailureMsg: //长链接重连失败
                this.netError.active = false;
                this.showTips(window.getLocalizedStr('NetworkError'));
                break;
            case MessageBaseBean_1.AutoMessageId.LinkExceptionMsg: //长链接异常
                this.netError.active = true;
                break;
            case MessageBaseBean_1.AutoMessageId.GameRouteNotFoundMsg: //游戏线路异常的通知
                this.showTips(autoMessageBean.data.code);
                break;
            case MessageBaseBean_1.AutoMessageId.SwitchGameSceneMsg: //切换游戏场景
                this.setCurrentPage(PageType.GAME_PAGE);
                break;
            case MessageBaseBean_1.AutoMessageId.WalletUpdateMsg: //更新金豆余额的通知
                //发送获取更新用户信息的消息
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeUserInfo, {});
                break;
            case MessageBaseBean_1.AutoMessageId.ServerCodeUpdateMsg: //更新 code 的通知
                Publish_1.Publish.GetInstance().code = autoMessageBean.data.code;
                break;
        }
    };
    //长链接消息(异常)
    GlobalManagerController.prototype.onErrorMessage = function (messageBean) {
        switch (messageBean.code) {
            case ErrorCode_1.ErrorCode.ErrInvalidInviteCode: //无效的邀请码
                this.hallPageController.joinError();
                break;
            case ErrorCode_1.ErrorCode.ErrRequestUser: //获取用户信息失败
                this.showTips(window.getLocalizedStr('GetUserInfoFailed'));
                // 断开WebSocket连接
                WebSocketTool_1.WebSocketTool.GetInstance().disconnect();
                break;
            case ErrorCode_1.ErrorCode.ErrNotFoundRoom: //没有找到指定的房间
                if (messageBean.msgId != MessageId_1.MessageId.MsgTypeMoveBlock) {
                    this.toastController.showContent(window.getLocalizedStr('RoomDoesNotExist'));
                    //没有找到房间 就直接返回到大厅页面
                    this.setCurrentPage(PageType.HALL_PAGE);
                }
                break;
            case ErrorCode_1.ErrorCode.ErrNotFoundUser: // 没有找到玩家信息
                if (messageBean.msgId === MessageId_1.MessageId.MsgTypeEnterRoom) { //只有在这个messageId下 才会踢出到大厅
                    //没有找到玩家信息 就直接返回到大厅页面
                    this.setCurrentPage(PageType.HALL_PAGE);
                }
                break;
            case ErrorCode_1.ErrorCode.ErrEnoughUser: //房间已满
                this.toastController.showContent(window.getLocalizedStr('RoomIsFull'));
                break;
            case ErrorCode_1.ErrorCode.ErrChangeBalance: //扣除金币失败
            case ErrorCode_1.ErrorCode.ErrNotEnoughCoin: //金币不足
                this.topUpDialogController.show(function () { });
                break;
            case ErrorCode_1.ErrorCode.ErrPlaying: //玩家已经在游戏中了
                //执行一遍 enterroom
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeEnterRoom, {}); //重连进来的 玩家请求进入房间
                break;
        }
    };
    //长链接消息(正常)
    GlobalManagerController.prototype.onMessage = function (messageBean) {
        switch (messageBean.msgId) {
            case MessageId_1.MessageId.MsgTypeCreateWs: //创建ws连接 成功  
                this.netError.active = false;
                //登录
                WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLogin, {});
                break;
            case MessageId_1.MessageId.MsgTypeLogin: //获取登录数据并存储
                GlobalBean_1.GlobalBean.GetInstance().loginData = messageBean.data;
                if (this.currentPage === PageType.START_UP_PAGE) {
                    //判断当前是否是在启动页面
                    this.startUpPageController.setLogin();
                }
                else {
                    this.hallPageController.updateGold();
                    this.hallPageController.LoginSuccess();
                    //没有在游戏中，但是还停留在游戏页面
                    if (GlobalBean_1.GlobalBean.GetInstance().loginData.roomId === 0 && this.currentPage === PageType.GAME_PAGE) {
                        this.setCurrentPage(PageType.HALL_PAGE); //返回到大厅
                    }
                }
                break;
            case MessageId_1.MessageId.MsgTypePairRequest: //开始匹配
                this.hallPageController.setHallOrMatch(HallPageController_1.HallOrMatch.MATCH_PARENT);
                this.hallPageController.createMatchView();
                break;
            case MessageId_1.MessageId.MsgTypeCancelPair: //取消匹配
                this.hallPageController.setHallOrMatch(HallPageController_1.HallOrMatch.HALL_PARENT);
                break;
            case MessageId_1.MessageId.MsgTypeGameStart: //游戏开始
                var noticeStartGame = messageBean.data;
                GlobalBean_1.GlobalBean.GetInstance().noticeStartGame = noticeStartGame; //存储游戏数据
                var index = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users.findIndex(function (item) { return item.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId; }); //搜索
                //把游戏开始之后最新的金币余额进行赋值
                if (index != -1) {
                    GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.coin = noticeStartGame.users[index].coin;
                }
                // 处理游戏开始数据，获取炸弹数量和地图类型
                this.gamePageController.onGameStart(noticeStartGame);
                if (noticeStartGame.roomType === EnumBean_1.RoomType.RoomTypeCommon) { // 房间类型 1-普通场 2-私人场
                    this.hallPageController.setGameData();
                }
                else {
                    this.setCurrentPage(PageType.GAME_PAGE); //开始游戏进入游戏页面
                }
                break;
            case MessageId_1.MessageId.MsgTypeEnterRoom: //重连的游戏数据
                var noticeStartGame2 = messageBean.data;
                GlobalBean_1.GlobalBean.GetInstance().noticeStartGame = noticeStartGame2; //存储游戏数据
                // 处理重连游戏数据，获取炸弹数量和地图类型
                this.gamePageController.onGameStart(noticeStartGame2);
                //跳转进游戏页面
                this.setCurrentPage(PageType.GAME_PAGE);
                break;
            case MessageId_1.MessageId.MsgTypeLeaveRoom: // 玩家主动离开房间
                var leaveRoomData = messageBean.data;
                // 检查是否是关卡游戏的LeaveRoom响应（包含levelId字段）
                if (leaveRoomData.levelId !== undefined) {
                    // 关卡游戏的LeaveRoom响应，直接返回大厅
                    cc.log("收到关卡游戏退出响应，返回大厅页面");
                    GlobalBean_1.GlobalBean.GetInstance().cleanData(); //清空数据
                    var autoMessageBean = {
                        'msgId': MessageBaseBean_1.AutoMessageId.JumpHallPage,
                        'data': { 'type': 2 } //2 是玩家主动离开游戏房间
                    };
                    GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
                }
                else if (leaveRoomData.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId) {
                    // 普通房间游戏的LeaveRoom响应
                    GlobalBean_1.GlobalBean.GetInstance().cleanData(); //清空数据
                    var autoMessageBean = {
                        'msgId': MessageBaseBean_1.AutoMessageId.JumpHallPage,
                        'data': { 'type': 2 } //2 是玩家主动离开游戏房间
                    };
                    GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.AutoMessage, autoMessageBean);
                }
                break;
            case MessageId_1.MessageId.MsgTypeCreateInvite: //创建邀请（也就是创建私人游戏房间）
                GlobalBean_1.GlobalBean.GetInstance().inviteInfo = messageBean.data;
                //点击 create 的回调
                this.hallPageController.joinCreateRoom();
                break;
            case MessageId_1.MessageId.MsgTypeAcceptInvite: //接受邀请
                var acceptInvite = messageBean.data;
                GlobalBean_1.GlobalBean.GetInstance().inviteInfo = acceptInvite.inviteInfo;
                this.hallPageController.setAcceptInvite(acceptInvite);
                break;
            case MessageId_1.MessageId.MsgTypeLeaveInvite: //收到离开房间的信息
                var noticeLeaveInvite = messageBean.data;
                this.hallPageController.leaveRoom(noticeLeaveInvite);
                break;
            case MessageId_1.MessageId.MsgTypeInviteReady: //收到有玩家准备的消息
                var noticeUserInviteStatus = messageBean.data;
                this.hallPageController.setReadyState(noticeUserInviteStatus);
                break;
            case MessageId_1.MessageId.MsgTypeNoticeInviteStatus: //广播邀请状态
                var noticeUserInviteStatus2 = messageBean.data;
                this.hallPageController.setReadyState(noticeUserInviteStatus2);
                break;
            case MessageId_1.MessageId.MsgTypeInviteKickOut: //收到玩家被踢出的信息
                var inviteKickOut = messageBean.data;
                if (inviteKickOut.userId === GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId) {
                    this.toastController.showContent(window.getLocalizedStr('KickOut'));
                    //被踢的是自己的话 直接返回大厅
                    this.setCurrentPage(PageType.HALL_PAGE);
                }
                else {
                    //这里拼接一下数据 走离开房间流程，其实是踢出房间
                    var noticeLeaveInvite1 = { 'userId': inviteKickOut.userId, 'isCreator': false };
                    this.hallPageController.leaveRoom(noticeLeaveInvite1);
                }
                break;
            case MessageId_1.MessageId.MsgTypeUserInfo: //更新用户信息的消息
                var userInfo = messageBean.data;
                GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo = userInfo;
                this.hallPageController.updateGold();
                break;
            case MessageId_1.MessageId.MsgTypeSettlement: //大结算
                var noticeSettlement = messageBean.data;
                this.gamePageController.setCongratsDialog(noticeSettlement);
                break;
            case MessageId_1.MessageId.MsgTypeNoticeRoundStart: //扫雷回合开始通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeRoundStart(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeNoticeActionDisplay: //扫雷操作展示通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeActionDisplay(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeNoticeRoundEnd: //扫雷回合结束通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeRoundEnd(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeNoticeFirstChoiceBonus: //扫雷首选玩家奖励通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onNoticeFirstChoiceBonus(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeAIStatusChange: //AI托管状态变更通知
                // 只有在游戏页面时才处理此消息
                if (this.currentPage === PageType.GAME_PAGE) {
                    this.gamePageController.onAIStatusChange(messageBean.data);
                }
                break;
            case MessageId_1.MessageId.MsgTypeExtendLevelProgress: //关卡进度
                if (this.currentPage === PageType.HALL_PAGE) {
                    // 从后端获取关卡进度并更新
                    var levelProgressData = messageBean.data;
                    if (levelProgressData) {
                        this.hallPageController.setLevelProgress(levelProgressData);
                    }
                }
                break;
            case MessageId_1.MessageId.MsgTypeExtendLevelInfo: //关卡信息
                if (this.currentPage === PageType.LEVEL_PAGE) {
                    // 从后端获取关卡信息并更新
                    var levelInfoData = messageBean.data;
                    if (levelInfoData) {
                        this.levelPageController.onExtendLevelInfo(levelInfoData);
                    }
                }
                break;
        }
    };
    //设置展示页面的
    GlobalManagerController.prototype.setCurrentPage = function (pageType) {
        this.currentPage = pageType;
        this.startUpPage.active = false;
        this.hallPage.active = false;
        this.gamePage.active = false;
        this.levelPage.active = false;
        switch (pageType) {
            case PageType.START_UP_PAGE:
                this.startUpPage.active = true;
                break;
            case PageType.HALL_PAGE:
                this.hallPage.active = true;
                break;
            case PageType.GAME_PAGE:
                this.gamePage.active = true;
                break;
            case PageType.LEVEL_PAGE:
                this.levelPage.active = true;
                break;
        }
    };
    __decorate([
        property(TipsDialogController_1.default)
    ], GlobalManagerController.prototype, "tipsDialogController", void 0);
    __decorate([
        property(TopUpDialogController_1.default)
    ], GlobalManagerController.prototype, "topUpDialogController", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "netError", void 0);
    __decorate([
        property(ToastController_1.default)
    ], GlobalManagerController.prototype, "toastController", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "startUpPage", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "hallPage", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "gamePage", void 0);
    __decorate([
        property(cc.Node)
    ], GlobalManagerController.prototype, "levelPage", void 0);
    GlobalManagerController = __decorate([
        ccclass
    ], GlobalManagerController);
    return GlobalManagerController;
}(cc.Component));
exports.default = GlobalManagerController;
if (!CC_EDITOR) {
    cc.Sprite.prototype["onLoad"] = function () {
        var _this = this;
        this.srcBlendFactor = cc.macro.BlendFactor.ONE;
        this.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
        // 延迟检查 spriteFrame，避免初始化时的警告
        this.scheduleOnce(function () {
            if (_this.spriteFrame && _this.spriteFrame.getTexture()) {
                _this.spriteFrame.getTexture().setPremultiplyAlpha(true);
            }
            // 移除警告，因为很多 Sprite 组件在初始化时确实没有 SpriteFrame
            // 这是正常的，不需要警告
        }, 0.1);
    };
    cc.Label.prototype["onLoad"] = function () {
        this.srcBlendFactor = cc.macro.BlendFactor.ONE;
        this.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
    };
    cc.macro.ALLOW_IMAGE_BITMAP = false; // 禁用 Bitmap 图片格式
}

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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