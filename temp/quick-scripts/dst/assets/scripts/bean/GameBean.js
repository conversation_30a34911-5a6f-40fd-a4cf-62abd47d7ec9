
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/bean/GameBean.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8d8d2RWlP1Coqs84m843tJg', 'GameBean');
// scripts/bean/GameBean.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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