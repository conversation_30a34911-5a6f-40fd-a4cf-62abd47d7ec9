
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/net/MessageId.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'f1db1fNQYxAjIe8lTfdT1iF', 'MessageId');
// scripts/net/MessageId.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageId = void 0;
//消息 id
var MessageId;
(function (MessageId) {
    MessageId["MsgTypeCreateWs"] = "CreateWs";
    MessageId["MsgTypeNoticeUserCoin"] = "NoticeUserCoin";
    MessageId["MsgTypeHeartbeat"] = "Heartbeat";
    MessageId["MsgTypeLogin"] = "Login";
    MessageId["MsgTypeUserInfo"] = "UserInfo";
    MessageId["MsgTypePairRequest"] = "PairRequest";
    MessageId["MsgTypeCancelPair"] = "CancelPair";
    MessageId["MsgTypePairResult"] = "PairResult";
    MessageId["MsgTypeEnterRoom"] = "EnterRoom";
    MessageId["MsgTypeSitDown"] = "SitDown";
    MessageId["MsgTypeRobotSitDown"] = "RobotSitDown";
    MessageId["MsgTypeStand"] = "Stand";
    MessageId["MsgTypeReady"] = "Ready";
    MessageId["MsgTypeLeaveRoom"] = "LeaveRoom";
    MessageId["MsgTypeUserOffline"] = "UserOffline";
    MessageId["MsgTypeKickOutUser"] = "KickOutUser";
    MessageId["MsgTypeCreateInvite"] = "CreateInvite";
    MessageId["MsgTypeAcceptInvite"] = "AcceptInvite";
    MessageId["MsgTypeInviteReady"] = "InviteReady";
    MessageId["MsgTypeChgInviteCfg"] = "ChgInviteCfg";
    MessageId["MsgTypeLeaveInvite"] = "LeaveInvite";
    MessageId["MsgTypeNoticeInviteStatus"] = "NoticeInviteStatus";
    MessageId["MsgTypeInviteKickOut"] = "InviteKickOut";
    MessageId["MsgTypeInviteStart"] = "InviteStart";
    MessageId["MsgTypeViewerList"] = "ViewerList";
    MessageId["MsgTypeGameStart"] = "GameStart";
    MessageId["MsgTypeFirstMove"] = "FirstMove";
    MessageId["MsgTypeFirstMoveEnd"] = "FirstMoveEnd";
    MessageId["MsgTypeUserPosList"] = "UserPosList";
    MessageId["MsgTypeRollDice"] = "RollDice";
    MessageId["MsgTypeMoveChess"] = "MoveChess";
    MessageId["MsgTypeUseProp"] = "UseProp";
    MessageId["MsgTypeChoiceProp"] = "ChoiceProp";
    MessageId["MsgTypeChoicePropResult"] = "ChoicePropResult";
    MessageId["MsgTypeChoiceAdvance"] = "ChoiceAdvance";
    MessageId["MsgTypeMoveChessEnd"] = "MoveChessEnd";
    MessageId["MsgTypeSettlement"] = "Settlement";
    MessageId["MsgTypeProductConfigs"] = "ProductConfigs";
    MessageId["MsgTypeBuyProduct"] = "BuyProduct";
    MessageId["MsgTypeSetSkin"] = "SetSkin";
    MessageId["MsgTypeMoveBlock"] = "MoveBlock";
    MessageId["MsgTypeScoreChg"] = "ScoreChg";
    // 扫雷游戏相关消息
    MessageId["MsgTypeNoticeRoundStart"] = "NoticeRoundStart";
    MessageId["MsgTypeNoticeActionDisplay"] = "NoticeActionDisplay";
    MessageId["MsgTypeNoticeRoundEnd"] = "NoticeRoundEnd";
    MessageId["MsgTypeNoticeFirstChoiceBonus"] = "NoticeFirstChoiceBonus";
    MessageId["MsgTypeNoticeGameEnd"] = "NoticeGameEnd";
    MessageId["MsgTypeClickBlock"] = "ClickBlock";
    // 关卡进度相关消息
    MessageId["MsgTypeExtendLevelProgress"] = "ExtendLevelProgress";
    MessageId["MsgTypeExtendLevelInfo"] = "ExtendLevelInfo";
    MessageId["MsgTypeLevelClickBlock"] = "LevelClickBlock";
    MessageId["MsgTypeLevelGameEnd"] = "LevelGameEnd";
    MessageId["MsgTypeEndLevelGame"] = "EndLevelGame";
    // AI托管相关消息
    MessageId["MsgTypeAIStatusChange"] = "AIStatusChange";
    MessageId["MsgTypeCancelAIManagement"] = "CancelAIManagement";
})(MessageId = exports.MessageId || (exports.MessageId = {}));

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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