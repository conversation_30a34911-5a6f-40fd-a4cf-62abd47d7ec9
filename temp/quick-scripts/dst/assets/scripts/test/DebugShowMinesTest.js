
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/test/DebugShowMinesTest.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '4d26ae/B4BKRLFRxcgY94Mp', 'DebugShowMinesTest');
// scripts/test/DebugShowMinesTest.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var DebugShowMinesTest = /** @class */ (function (_super) {
    __extends(DebugShowMinesTest, _super);
    function DebugShowMinesTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.testButton = null;
        _this.statusLabel = null;
        return _this;
    }
    DebugShowMinesTest.prototype.start = function () {
        if (this.testButton) {
            this.testButton.node.on('click', this.sendDebugShowMinesMessage, this);
        }
        if (this.statusLabel) {
            this.statusLabel.string = '点击按钮测试DebugShowMines消息';
        }
    };
    // 发送测试的DebugShowMines消息
    DebugShowMinesTest.prototype.sendDebugShowMinesMessage = function () {
        var _this = this;
        cc.log("发送DebugShowMines测试消息");
        // 发送DebugShowMines消息到服务器
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeDebugShowMines, {});
        // 模拟服务器响应（用于测试）
        this.scheduleOnce(function () {
            _this.simulateServerResponse();
        }, 1.0);
        if (this.statusLabel) {
            this.statusLabel.string = '已发送DebugShowMines消息';
        }
    };
    // 模拟服务器响应
    DebugShowMinesTest.prototype.simulateServerResponse = function () {
        // 创建模拟的地雷位置数据
        var testMinePositions = [
            { x: 1, y: 1 },
            { x: 3, y: 2 },
            { x: 5, y: 4 },
            { x: 2, y: 6 },
            { x: 7, y: 3 }
        ];
        // 模拟接收到的消息格式
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeDebugShowMines,
            code: 0,
            msg: "success",
            data: testMinePositions
        };
        // 发送消息事件
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u6A21\u62DF\u54CD\u5E94\uFF1A\u663E\u793A" + testMinePositions.length + "\u4E2A\u5730\u96F7\u4F4D\u7F6E";
        }
    };
    DebugShowMinesTest.prototype.onDestroy = function () {
        if (this.testButton) {
            this.testButton.node.off('click', this.sendDebugShowMinesMessage, this);
        }
    };
    __decorate([
        property(cc.Button)
    ], DebugShowMinesTest.prototype, "testButton", void 0);
    __decorate([
        property(cc.Label)
    ], DebugShowMinesTest.prototype, "statusLabel", void 0);
    DebugShowMinesTest = __decorate([
        ccclass
    ], DebugShowMinesTest);
    return DebugShowMinesTest;
}(cc.Component));
exports.default = DebugShowMinesTest;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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