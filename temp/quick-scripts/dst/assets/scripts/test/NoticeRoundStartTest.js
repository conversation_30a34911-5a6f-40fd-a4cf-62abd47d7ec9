
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/test/NoticeRoundStartTest.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a1b2cPU5fZ4kKvN7xI0VniQ', 'NoticeRoundStartTest');
// scripts/test/NoticeRoundStartTest.ts

"use strict";
// 测试NoticeRoundStart消息处理的脚本
// 这个脚本可以用来模拟发送NoticeRoundStart消息，测试前端计时器更新功能
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var MessageId_1 = require("../net/MessageId");
var GlobalBean_1 = require("../bean/GlobalBean");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var NoticeRoundStartTest = /** @class */ (function (_super) {
    __extends(NoticeRoundStartTest, _super);
    function NoticeRoundStartTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.testButton = null;
        _this.firstChoiceTestButton = null;
        _this.testGameStartAnimationBtn = null;
        _this.testRoundStartAnimationBtn = null;
        _this.testAIStatusChangeBtn = null;
        _this.statusLabel = null;
        return _this;
    }
    NoticeRoundStartTest.prototype.start = function () {
        if (this.testButton) {
            this.testButton.node.on('click', this.sendTestMessage, this);
        }
        if (this.firstChoiceTestButton) {
            this.firstChoiceTestButton.node.on('click', this.testFirstChoiceBonusFlow, this);
        }
        if (this.testGameStartAnimationBtn) {
            this.testGameStartAnimationBtn.node.on('click', this.testGameStartAnimation, this);
        }
        if (this.testRoundStartAnimationBtn) {
            this.testRoundStartAnimationBtn.node.on('click', this.testRoundStartAnimation, this);
        }
        if (this.testAIStatusChangeBtn) {
            this.testAIStatusChangeBtn.node.on('click', this.testAIStatusChange, this);
        }
        if (this.statusLabel) {
            this.statusLabel.string = '点击按钮测试消息';
        }
    };
    // 发送测试的NoticeRoundStart消息
    NoticeRoundStartTest.prototype.sendTestMessage = function () {
        // 创建测试数据
        var testData = {
            roundNumber: 1,
            countDown: 25,
            gameStatus: 0
        };
        // 模拟接收到的消息格式
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeRoundStart,
            code: 0,
            msg: "success",
            data: testData
        };
        // 发送消息事件
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001\u6D4B\u8BD5\u6D88\u606F: \u56DE\u5408" + testData.roundNumber + ", \u5012\u8BA1\u65F6" + testData.countDown + "\u79D2";
        }
    };
    // 发送倒计时更新测试
    NoticeRoundStartTest.prototype.sendCountdownUpdate = function (seconds) {
        var testData = {
            roundNumber: 1,
            countDown: seconds,
            gameStatus: 0
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeRoundStart,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5012\u8BA1\u65F6\u66F4\u65B0: " + seconds + "\u79D2";
        }
    };
    // 测试不同的倒计时值
    NoticeRoundStartTest.prototype.testDifferentCountdowns = function () {
        var _this = this;
        // 测试25秒倒计时
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(25);
        }, 1);
        // 测试20秒倒计时（进入展示阶段）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(20);
        }, 3);
        // 测试5秒倒计时（回合结束前）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(5);
        }, 5);
        // 测试0秒倒计时（回合结束）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(0);
        }, 7);
    };
    // 发送NoticeActionDisplay测试消息
    NoticeRoundStartTest.prototype.sendActionDisplayMessage = function () {
        var testData = {
            roundNumber: 1,
            gameStatus: 0,
            countDown: 5,
            playerActions: [
                {
                    userId: "player_001",
                    x: 3,
                    y: 2,
                    action: 1,
                    score: 1,
                    isFirstChoice: true,
                    result: 2 // 数字2
                },
                {
                    userId: "player_002",
                    x: 1,
                    y: 4,
                    action: 2,
                    score: 1,
                    isFirstChoice: false,
                    result: "correct_mark"
                }
            ],
            playerTotalScores: {
                "player_001": 5,
                "player_002": 3
            },
            remainingMines: 10,
            message: "展示阶段：显示所有玩家操作"
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeActionDisplay,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001ActionDisplay\u6D88\u606F: \u5C55\u793A\u9636\u6BB5\uFF0C\u5269\u4F59" + testData.countDown + "\u79D2\uFF0C\u5269\u4F59\u70B8\u5F39" + testData.remainingMines + "\u4E2A";
        }
    };
    // 测试完整的回合流程
    NoticeRoundStartTest.prototype.testFullRoundFlow = function () {
        var _this = this;
        // 1. 发送回合开始
        this.sendTestMessage();
        // 2. 20秒后发送操作展示
        this.scheduleOnce(function () {
            _this.sendActionDisplayMessage();
        }, 2);
        if (this.statusLabel) {
            this.statusLabel.string = '开始测试完整回合流程...';
        }
    };
    // 测试先手奖励和后续加分流程
    NoticeRoundStartTest.prototype.testFirstChoiceBonusFlow = function () {
        var _this = this;
        // 1. 发送回合开始
        this.sendTestMessage();
        // 2. 2秒后发送先手奖励
        this.scheduleOnce(function () {
            _this.sendFirstChoiceBonusMessage();
        }, 2);
        // 3. 4秒后发送操作展示（包含先手玩家）
        this.scheduleOnce(function () {
            _this.sendActionDisplayWithFirstChoiceMessage();
        }, 4);
        if (this.statusLabel) {
            this.statusLabel.string = '测试先手奖励+本回合加分...';
        }
    };
    // 发送NoticeFirstChoiceBonus测试消息
    NoticeRoundStartTest.prototype.sendFirstChoiceBonusMessage = function () {
        var testData = {
            userId: "player_001",
            roundNumber: 1,
            bonusScore: 1,
            totalScore: 6 // 原来5分 + 1分先手奖励
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeFirstChoiceBonus,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001FirstChoiceBonus: player_001\u83B7\u5F97+1\u5148\u624B\u5956\u52B1";
        }
    };
    // 发送包含先手玩家的NoticeActionDisplay测试消息
    NoticeRoundStartTest.prototype.sendActionDisplayWithFirstChoiceMessage = function () {
        var testData = {
            roundNumber: 1,
            gameStatus: 0,
            countDown: 5,
            playerActions: [
                {
                    userId: "player_001",
                    x: 3,
                    y: 2,
                    action: 1,
                    score: 2,
                    isFirstChoice: true,
                    result: 3 // 数字3
                },
                {
                    userId: "player_002",
                    x: 1,
                    y: 4,
                    action: 2,
                    score: 1,
                    isFirstChoice: false,
                    result: "correct_mark"
                }
            ],
            playerTotalScores: {
                "player_001": 8,
                "player_002": 4 // 原来3分 + 本回合1分
            },
            remainingMines: 9,
            message: "展示阶段：先手玩家应该显示两次player_game_pfb分数变化"
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeActionDisplay,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001ActionDisplay: \u5148\u624B\u73A9\u5BB6\u5E94\u663E\u793A+2\u5206";
        }
    };
    /**
     * 测试游戏开始动画
     */
    NoticeRoundStartTest.prototype.testGameStartAnimation = function () {
        var _this = this;
        if (this.statusLabel) {
            this.statusLabel.string = "测试游戏开始动画...";
        }
        // 获取GamePageController实例
        var gamePageController = window.gamePageController;
        if (gamePageController) {
            // 调用游戏开始动画
            if (gamePageController.showGameStartAnimation) {
                gamePageController.showGameStartAnimation();
                if (this.statusLabel) {
                    this.statusLabel.string = "游戏开始动画已触发";
                }
                // 3秒后隐藏
                this.scheduleOnce(function () {
                    if (gamePageController.hideGameStartAnimation) {
                        gamePageController.hideGameStartAnimation();
                        if (_this.statusLabel) {
                            _this.statusLabel.string = "游戏开始动画已隐藏";
                        }
                    }
                }, 3);
            }
            else {
                if (this.statusLabel) {
                    this.statusLabel.string = "GamePageController中没有找到showGameStartAnimation方法";
                }
            }
        }
        else {
            if (this.statusLabel) {
                this.statusLabel.string = "未找到GamePageController实例";
            }
        }
    };
    /**
     * 测试回合开始动画
     */
    NoticeRoundStartTest.prototype.testRoundStartAnimation = function () {
        if (this.statusLabel) {
            this.statusLabel.string = "测试回合开始动画...";
        }
        // 获取GamePageController实例
        var gamePageController = window.gamePageController;
        if (gamePageController) {
            // 调用回合开始动画
            if (gamePageController.showRoundStartAnimation) {
                gamePageController.showRoundStartAnimation();
                if (this.statusLabel) {
                    this.statusLabel.string = "回合开始动画已触发";
                }
            }
            else {
                if (this.statusLabel) {
                    this.statusLabel.string = "GamePageController中没有找到showRoundStartAnimation方法";
                }
            }
        }
        else {
            if (this.statusLabel) {
                this.statusLabel.string = "未找到GamePageController实例";
            }
        }
    };
    // 测试AI托管状态变更消息
    NoticeRoundStartTest.prototype.testAIStatusChange = function () {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID，用于测试
        var currentUserId = ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "player_001";
        var testData = {
            userId: currentUserId,
            isAIManaged: true,
            timestamp: Date.now()
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeAIStatusChange,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001AI\u6258\u7BA1\u72B6\u6001\u53D8\u66F4: " + currentUserId + "\u8FDB\u5165\u6258\u7BA1\uFF0C\u5E94\u663E\u793A\u6258\u7BA1\u9875\u9762";
        }
        // 5秒后发送退出托管的消息（给用户足够时间测试点击）
        this.scheduleOnce(function () {
            var exitData = {
                userId: currentUserId,
                isAIManaged: false,
                timestamp: Date.now()
            };
            var exitMessageBean = {
                msgId: MessageId_1.MessageId.MsgTypeAIStatusChange,
                code: 0,
                msg: "success",
                data: exitData
            };
            GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, exitMessageBean);
            if (_this.statusLabel) {
                _this.statusLabel.string = "\u5DF2\u53D1\u9001AI\u6258\u7BA1\u72B6\u6001\u53D8\u66F4: " + currentUserId + "\u9000\u51FA\u6258\u7BA1\uFF0C\u5E94\u9690\u85CF\u6258\u7BA1\u9875\u9762";
            }
        }, 5.0);
    };
    NoticeRoundStartTest.prototype.onDestroy = function () {
        if (this.testButton) {
            this.testButton.node.off('click', this.sendTestMessage, this);
        }
        if (this.firstChoiceTestButton) {
            this.firstChoiceTestButton.node.off('click', this.testFirstChoiceBonusFlow, this);
        }
        if (this.testGameStartAnimationBtn) {
            this.testGameStartAnimationBtn.node.off('click', this.testGameStartAnimation, this);
        }
        if (this.testRoundStartAnimationBtn) {
            this.testRoundStartAnimationBtn.node.off('click', this.testRoundStartAnimation, this);
        }
        if (this.testAIStatusChangeBtn) {
            this.testAIStatusChangeBtn.node.off('click', this.testAIStatusChange, this);
        }
    };
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testButton", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "firstChoiceTestButton", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testGameStartAnimationBtn", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testRoundStartAnimationBtn", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testAIStatusChangeBtn", void 0);
    __decorate([
        property(cc.Label)
    ], NoticeRoundStartTest.prototype, "statusLabel", void 0);
    NoticeRoundStartTest = __decorate([
        ccclass
    ], NoticeRoundStartTest);
    return NoticeRoundStartTest;
}(cc.Component));
exports.default = NoticeRoundStartTest;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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