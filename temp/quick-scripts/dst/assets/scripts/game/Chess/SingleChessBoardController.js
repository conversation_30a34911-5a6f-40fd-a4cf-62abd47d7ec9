
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/SingleChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '04af7LUaqhJFrIS/DTERlBy', 'SingleChessBoardController');
// scripts/game/Chess/SingleChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../../net/WebSocketManager");
var MessageId_1 = require("../../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 五种棋盘配置
var BOARD_CONFIGS = {
    "8x8": {
        width: 752,
        height: 752,
        rows: 8,
        cols: 8,
        gridWidth: 88,
        gridHeight: 88
    },
    "8x9": {
        width: 752,
        height: 845,
        rows: 9,
        cols: 8,
        gridWidth: 88,
        gridHeight: 88
    },
    "9x9": {
        width: 752,
        height: 747,
        rows: 9,
        cols: 9,
        gridWidth: 76,
        gridHeight: 76
    },
    "9x10": {
        width: 752,
        height: 830,
        rows: 10,
        cols: 9,
        gridWidth: 78,
        gridHeight: 78
    },
    "10x10": {
        width: 752,
        height: 745,
        rows: 10,
        cols: 10,
        gridWidth: 69,
        gridHeight: 69
    }
};
var SingleChessBoardController = /** @class */ (function (_super) {
    __extends(SingleChessBoardController, _super);
    function SingleChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boomPrefab = null; // boom预制体
        _this.biaojiPrefab = null; // biaoji预制体
        _this.boom1Prefab = null; // 数字1预制体
        _this.boom2Prefab = null; // 数字2预制体
        _this.boom3Prefab = null; // 数字3预制体
        _this.boom4Prefab = null; // 数字4预制体
        _this.boom5Prefab = null; // 数字5预制体
        _this.boom6Prefab = null; // 数字6预制体
        _this.boom7Prefab = null; // 数字7预制体
        _this.boom8Prefab = null; // 数字8预制体
        // 五个棋盘节点
        _this.qipan8x8Node = null; // 8x8棋盘节点
        _this.qipan8x9Node = null; // 8x9棋盘节点
        _this.qipan9x9Node = null; // 9x9棋盘节点
        _this.qipan9x10Node = null; // 9x10棋盘节点
        _this.qipan10x10Node = null; // 10x10棋盘节点
        // 当前使用的棋盘节点
        _this.currentBoardNode = null;
        // 当前棋盘配置
        _this.currentBoardConfig = null;
        _this.currentBoardType = "8x8"; // 默认8x8棋盘
        // 格子数据存储
        _this.gridData = []; // 二维数组存储格子数据
        _this.gridNodes = []; // 二维数组存储格子节点
        // 防重复发送消息
        _this.lastClickTime = 0;
        _this.lastClickPosition = "";
        _this.CLICK_COOLDOWN = 200; // 200毫秒冷却时间
        return _this;
    }
    SingleChessBoardController.prototype.onLoad = function () {
        // 不进行默认初始化，等待外部调用initBoard
    };
    SingleChessBoardController.prototype.start = function () {
        // start方法不再自动启用触摸事件，避免与initBoard重复
        // 触摸事件的启用由initBoard方法负责
    };
    /**
     * 根据棋盘类型获取对应的棋盘节点
     * @param boardType 棋盘类型
     */
    SingleChessBoardController.prototype.getBoardNodeByType = function (boardType) {
        switch (boardType) {
            case "8x8":
                return this.qipan8x8Node;
            case "8x9":
                return this.qipan8x9Node;
            case "9x9":
                return this.qipan9x9Node;
            case "9x10":
                return this.qipan9x10Node;
            case "10x10":
                return this.qipan10x10Node;
            default:
                return null;
        }
    };
    /**
     * 初始化指定类型的棋盘
     * @param boardType 棋盘类型 ("8x8", "8x9", "9x9", "9x10", "10x10")
     */
    SingleChessBoardController.prototype.initBoard = function (boardType) {
        if (!BOARD_CONFIGS[boardType]) {
            console.error("\u4E0D\u652F\u6301\u7684\u68CB\u76D8\u7C7B\u578B: " + boardType);
            return;
        }
        // 根据棋盘类型获取对应的节点
        this.currentBoardNode = this.getBoardNodeByType(boardType);
        if (!this.currentBoardNode) {
            console.error("\u68CB\u76D8\u8282\u70B9\u672A\u8BBE\u7F6E\uFF01\u68CB\u76D8\u7C7B\u578B: " + boardType);
            return;
        }
        this.currentBoardType = boardType;
        this.currentBoardConfig = BOARD_CONFIGS[boardType];
        // 清空现有数据
        this.gridData = [];
        this.gridNodes = [];
        // 初始化数据数组
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    worldPos: this.getGridWorldPosition(x, y),
                    hasPlayer: false
                };
            }
        }
        this.createGridNodes();
    };
    // 启用现有格子的触摸事件
    SingleChessBoardController.prototype.createGridNodes = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    SingleChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.currentBoardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 尝试从节点名称解析坐标
            var coords = this.parseGridCoordinateFromName(child.name);
            if (coords) {
                this.setupGridTouchEvents(child, coords.x, coords.y);
                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];
                this.gridNodes[coords.x][coords.y] = child;
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getGridCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupGridTouchEvents(child, coords_1.x, coords_1.y);
                    this.gridNodes[coords_1.x] = this.gridNodes[coords_1.x] || [];
                    this.gridNodes[coords_1.x][coords_1.y] = child;
                }
            }
        }
    };
    // 从节点名称解析格子坐标
    SingleChessBoardController.prototype.parseGridCoordinateFromName = function (nodeName) {
        // 尝试匹配 Grid_x_y 格式
        var match = nodeName.match(/Grid_(\d+)_(\d+)/);
        if (match) {
            return { x: parseInt(match[1]), y: parseInt(match[2]) };
        }
        return null;
    };
    // 从位置计算格子坐标（需要考虑不同棋盘类型的边距）
    SingleChessBoardController.prototype.getGridCoordinateFromPosition = function (pos) {
        if (!this.currentBoardConfig)
            return null;
        // 根据不同棋盘类型使用不同的计算方式
        switch (this.currentBoardType) {
            case "8x9":
                return this.getGridCoordinateFromPositionFor8x9(pos);
            case "9x9":
                return this.getGridCoordinateFromPositionFor9x9(pos);
            case "9x10":
                return this.getGridCoordinateFromPositionFor9x10(pos);
            case "10x10":
                return this.getGridCoordinateFromPositionFor10x10(pos);
            default:
                // 默认计算方式（适用于其他棋盘类型）
                var x = Math.floor((pos.x + this.currentBoardConfig.width / 2) / this.currentBoardConfig.gridWidth);
                var y = Math.floor((pos.y + this.currentBoardConfig.height / 2) / this.currentBoardConfig.gridHeight);
                if (this.isValidCoordinate(x, y)) {
                    return { x: x, y: y };
                }
                return null;
        }
    };
    // 8x9棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor8x9 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -321; // 左下角X坐标
        var startY = -364; // 左下角Y坐标
        var stepX = 91.14; // X方向精确步长
        var stepY = 91.125; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 9x9棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor9x9 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -322; // 左下角X坐标
        var startY = -320; // 左下角Y坐标
        var stepX = 80.25; // X方向精确步长
        var stepY = 80.375; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 9x10棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor9x10 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -320; // 左下角X坐标
        var startY = -361; // 左下角Y坐标
        var stepX = 80; // X方向精确步长
        var stepY = 80.33; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 10x10棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor10x10 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -328; // 左下角X坐标
        var startY = -322; // 左下角Y坐标
        var stepX = 72.56; // X方向精确步长
        var stepY = 72; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        else {
            return null;
        }
    };
    // 为格子设置触摸事件
    SingleChessBoardController.prototype.setupGridTouchEvents = function (gridNode, x, y) {
        var _this = this;
        // 先清除已有的触摸事件，防止重复绑定
        gridNode.off(cc.Node.EventType.TOUCH_START);
        gridNode.off(cc.Node.EventType.TOUCH_END);
        gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var hasTriggeredLongPress = false; // 标记是否已触发长按
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            hasTriggeredLongPress = false; // 重置长按标记
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME && !hasTriggeredLongPress) {
                        hasTriggeredLongPress = true; // 标记已触发长按
                        isLongPressing = false; // 立即停止长按状态，防止触摸结束时执行点击
                        // 执行长按事件
                        _this.onGridLongPress(x, y);
                        // 停止长按检测
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                            longPressCallback = null;
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            // 停止长按检测
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
                longPressCallback = null;
            }
            // 严格检查：只有在所有条件都满足的情况下才执行点击事件
            var shouldExecuteClick = isLongPressing &&
                longPressTimer < LONG_PRESS_TIME &&
                !hasTriggeredLongPress;
            if (shouldExecuteClick) {
                _this.onGridClick(x, y, event);
            }
            else {
            }
            // 清理长按检测
            isLongPressing = false;
            hasTriggeredLongPress = false;
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            // 清理长按检测
            isLongPressing = false;
            hasTriggeredLongPress = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
                longPressCallback = null;
            }
        }, this);
    };
    // 计算格子的世界坐标位置（左下角为(0,0)）
    SingleChessBoardController.prototype.getGridWorldPosition = function (x, y) {
        if (!this.currentBoardConfig)
            return cc.v2(0, 0);
        // 计算格子中心点位置
        // 左下角为(0,0)，所以y坐标需要从下往上计算
        var posX = (x * this.currentBoardConfig.gridWidth) + (this.currentBoardConfig.gridWidth / 2) - (this.currentBoardConfig.width / 2);
        var posY = (y * this.currentBoardConfig.gridHeight) + (this.currentBoardConfig.gridHeight / 2) - (this.currentBoardConfig.height / 2);
        return cc.v2(posX, posY);
    };
    // 格子点击事件 - 发送挖掘操作
    SingleChessBoardController.prototype.onGridClick = function (x, y, _event) {
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有任何预制体（包括biaoji）
        if (this.gridData[x][y].hasPlayer) {
            return;
        }
        // 发送LevelClickBlock消息
        this.sendLevelClickBlock(x, y, 1);
    };
    // 格子长按事件 - 标记/取消标记操作（参考联机版逻辑）
    SingleChessBoardController.prototype.onGridLongPress = function (x, y) {
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有biaoji预制体
        if (this.hasBiaojiAt(x, y)) {
            // 如果已经有biaoji，则删除它（本地立即处理）
            this.removeBiaojiAt(x, y);
            // 发送取消标记消息（但不等待响应，因为已经本地处理了）
            this.sendLevelClickBlock(x, y, 2);
        }
        else if (!this.gridData[x][y].hasPlayer) {
            // 如果没有任何预制体，则生成biaoji（本地立即处理）
            this.createBiaojiPrefab(x, y);
            // 发送标记消息（但不等待响应，因为已经本地处理了）
            this.sendLevelClickBlock(x, y, 2);
        }
        else {
            // 如果有其他类型的预制体（如数字、boom），则不处理
        }
    };
    // 发送LevelClickBlock消息
    SingleChessBoardController.prototype.sendLevelClickBlock = function (x, y, action) {
        // 防重复发送检查
        var currentTime = Date.now();
        var positionKey = x + "," + y + "," + action;
        if (currentTime - this.lastClickTime < this.CLICK_COOLDOWN && this.lastClickPosition === positionKey) {
            return;
        }
        this.lastClickTime = currentTime;
        this.lastClickPosition = positionKey;
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLevelClickBlock, clickData);
    };
    // 检查坐标是否有效
    SingleChessBoardController.prototype.isValidCoordinate = function (x, y) {
        if (!this.currentBoardConfig)
            return false;
        return x >= 0 && x < this.currentBoardConfig.cols && y >= 0 && y < this.currentBoardConfig.rows;
    };
    /**
     * 在指定位置创建biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    SingleChessBoardController.prototype.createBiaojiPrefab = function (x, y) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";
        // 设置位置
        var position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(biaojiNode);
        // 播放出现动画，10x10棋盘使用0.8缩放
        var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
        // 更新格子数据
        this.gridData[x][y].hasPlayer = true;
        this.gridData[x][y].playerNode = biaojiNode;
    };
    /**
     * 在指定位置创建boom预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param isCurrentUser 是否是当前用户点到的雷（可选，默认为true以保持向后兼容）
     */
    SingleChessBoardController.prototype.createBoomPrefab = function (x, y, isCurrentUser) {
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("SingleChessBoardController: boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";
        // 设置位置
        var position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(boomNode);
        // 播放出现动画，10x10棋盘使用0.8缩放
        var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
        var bounceScale = targetScale * 1.2; // 弹跳效果比目标缩放大20%
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: bounceScale, scaleY: bounceScale }, { easing: 'backOut' })
            .to(0.1, { scaleX: targetScale, scaleY: targetScale })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }
        // 更新格子数据
        this.gridData[x][y].hasPlayer = true;
        this.gridData[x][y].playerNode = boomNode;
    };
    /**
     * 在指定位置创建数字预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字(1-8)
     */
    SingleChessBoardController.prototype.createNumberPrefab = function (x, y, number) {
        if (number < 1 || number > 8) {
            console.error("\u65E0\u6548\u7684\u6570\u5B57: " + number);
            return;
        }
        // 获取对应的数字预制体
        var prefab = this.getNumberPrefab(number);
        if (!prefab) {
            console.error("\u6570\u5B57" + number + "\u9884\u5236\u4F53\u672A\u8BBE\u7F6E");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "Boom" + number;
        // 设置位置
        var position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(numberNode);
        // 播放出现动画，10x10棋盘使用0.8缩放
        var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
    };
    /**
     * 在指定位置创建自定义预制体（用于测试等功能）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param prefab 要创建的预制体
     * @param name 节点名称
     */
    SingleChessBoardController.prototype.createCustomPrefab = function (x, y, prefab, name) {
        if (name === void 0) { name = "CustomPrefab"; }
        if (!prefab) {
            console.error("预制体未设置");
            return;
        }
        // 实例化预制体
        var customNode = cc.instantiate(prefab);
        customNode.name = name;
        // 设置位置
        var position = this.calculatePrefabPosition(x, y);
        customNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(customNode);
        // 播放出现动画，10x10棋盘使用0.8缩放
        var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
        customNode.setScale(0);
        cc.tween(customNode)
            .to(0.3, { scaleX: targetScale * 1.2, scaleY: targetScale * 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: targetScale, scaleY: targetScale })
            .start();
        return customNode;
    };
    // 获取数字预制体
    SingleChessBoardController.prototype.getNumberPrefab = function (number) {
        switch (number) {
            case 1: return this.boom1Prefab;
            case 2: return this.boom2Prefab;
            case 3: return this.boom3Prefab;
            case 4: return this.boom4Prefab;
            case 5: return this.boom5Prefab;
            case 6: return this.boom6Prefab;
            case 7: return this.boom7Prefab;
            case 8: return this.boom8Prefab;
            default: return null;
        }
    };
    /**
     * 计算预制体的精确位置（参考联机版ChessBoardController）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 预制体应该放置的精确位置
     */
    SingleChessBoardController.prototype.calculatePrefabPosition = function (x, y) {
        if (!this.currentBoardConfig) {
            return cc.v2(0, 0);
        }
        // 根据不同棋盘类型使用不同的计算方式
        switch (this.currentBoardType) {
            case "8x8":
                return this.calculatePrefabPositionFor8x8(x, y);
            case "8x9":
                return this.calculatePrefabPositionFor8x9(x, y);
            case "9x9":
                return this.calculatePrefabPositionFor9x9(x, y);
            case "9x10":
                return this.calculatePrefabPositionFor9x10(x, y);
            case "10x10":
                return this.calculatePrefabPositionFor10x10(x, y);
            default:
                return this.getGridWorldPosition(x, y);
        }
    };
    // 8x8棋盘的预制体位置计算（参考联机版）
    SingleChessBoardController.prototype.calculatePrefabPositionFor8x8 = function (x, y) {
        // 根据联机版的坐标规律计算：
        // (0,0) → (-314, -310)
        // (1,0) → (-224, -310)  // x增加90
        // (0,1) → (-314, -222)  // y增加88
        // (7,7) → (310, 312)
        var startX = -314; // 起始X坐标
        var startY = -310; // 起始Y坐标
        var stepX = 90; // X方向步长
        var stepY = 88; // Y方向步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 8x9棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor8x9 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-321, -364)
        // 右下角(7,0)：(317, -364)
        // 左上角(0,8)：(-321, 365)
        // 右上角(7,8)：(317, 365)
        // 计算步长：
        // X方向步长：(317 - (-321)) / 7 = 638 / 7 ≈ 91.14
        // Y方向步长：(365 - (-364)) / 8 = 729 / 8 ≈ 91.125
        var startX = -321; // 左下角X坐标
        var startY = -364; // 左下角Y坐标
        var stepX = 91.14; // X方向精确步长
        var stepY = 91.125; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 9x9棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor9x9 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-322, -320)
        // 右下角(8,0)：(320, -320)
        // 左上角(0,8)：(-322, 365)
        // 右上角(8,8)：(320, 323)
        // 计算步长：
        // X方向步长：(320 - (-322)) / 8 = 642 / 8 = 80.25
        // Y方向步长：(323 - (-320)) / 8 = 643 / 8 = 80.375
        var startX = -322; // 左下角X坐标
        var startY = -320; // 左下角Y坐标
        var stepX = 80.25; // X方向精确步长
        var stepY = 80.375; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 9x10棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor9x10 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-320, -361)
        // 右下角(8,0)：(320, -361)
        // 左上角(0,9)：(-320, 362)
        // 右上角(8,9)：(320, 362)
        // 计算步长：
        // X方向步长：(320 - (-320)) / 8 = 640 / 8 = 80
        // Y方向步长：(362 - (-361)) / 9 = 723 / 9 = 80.33
        var startX = -320; // 左下角X坐标
        var startY = -361; // 左下角Y坐标
        var stepX = 80; // X方向精确步长
        var stepY = 80.33; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 10x10棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor10x10 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-328, -322)
        // 右下角(9,0)：(325, -322)
        // 左上角(0,9)：(-328, 326)
        // 右上角(9,9)：(325, 326)
        // 计算步长：
        // X方向步长：(325 - (-328)) / 9 = 653 / 9 = 72.56
        // Y方向步长：(326 - (-322)) / 9 = 648 / 9 = 72
        var startX = -328; // 左下角X坐标
        var startY = -322; // 左下角Y坐标
        var stepX = 72.56; // X方向精确步长
        var stepY = 72; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 播放棋盘震动动画
    SingleChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.currentBoardNode)
            return;
        var originalPosition = this.currentBoardNode.getPosition();
        var shakeIntensity = 10;
        cc.tween(this.currentBoardNode)
            .repeat(5, cc.tween()
            .to(0.05, { position: cc.v3(originalPosition.x + shakeIntensity, originalPosition.y, 0) })
            .to(0.05, { position: cc.v3(originalPosition.x - shakeIntensity, originalPosition.y, 0) }))
            .to(0.1, { position: cc.v3(originalPosition.x, originalPosition.y, 0) })
            .start();
    };
    /**
     * 显示所有隐藏的格子（游戏结束时调用）
     */
    SingleChessBoardController.prototype.showAllHiddenGrids = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法显示隐藏格子！");
            return;
        }
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (var i = 0; i < this.currentBoardNode.children.length; i++) {
            var child = this.currentBoardNode.children[i];
            // 如果是小格子节点
            if (child.name.startsWith("Grid_") || child.name === "block") {
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
    };
    /**
     * 清除所有预制体（游戏结束时调用）
     */
    SingleChessBoardController.prototype.clearAllPrefabs = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法清除预制体！");
            return;
        }
        var childrenToRemove = [];
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.currentBoardNode.children.length; i++) {
            var child = this.currentBoardNode.children[i];
            // 检查是否是预制体（通过名称判断）
            if (child.name === "Biaoji" || child.name === "Boom" || child.name.startsWith("Boom")) {
                childrenToRemove.push(child);
            }
        }
        // 移除找到的预制体
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
        // 重置格子数据
        this.reinitializeBoardData();
    };
    /**
     * 重新初始化棋盘数据（仅在开始新游戏时调用）
     */
    SingleChessBoardController.prototype.reinitializeBoardData = function () {
        if (!this.currentBoardConfig)
            return;
        // 重置gridData中的预制体状态
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                if (this.gridData[x] && this.gridData[x][y]) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }
    };
    /**
     * 处理ExtendLevelInfo消息（游戏结束时调用）
     */
    SingleChessBoardController.prototype.onExtendLevelInfo = function () {
        this.showAllHiddenGrids();
        this.clearAllPrefabs();
    };
    /**
     * 处理LevelGameEnd消息（游戏结束时调用）
     * 注意：不清理任何数据，保持玩家的游玩痕迹
     */
    SingleChessBoardController.prototype.onLevelGameEnd = function () {
        // 不显示隐藏的格子，保持当前状态
        // 不清理预制体，不重置格子状态，保持游戏结果显示
        // 让玩家可以看到自己的标记（biaoji）、挖掘结果（数字、boom）等
    };
    /**
     * 隐藏指定位置的格子（点击时调用）
     */
    SingleChessBoardController.prototype.hideGridAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            // 使用动画隐藏格子
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridNode.active = false;
            })
                .start();
        }
    };
    /**
     * 获取当前棋盘类型
     */
    SingleChessBoardController.prototype.getCurrentBoardType = function () {
        return this.currentBoardType;
    };
    /**
     * 获取当前棋盘配置
     */
    SingleChessBoardController.prototype.getCurrentBoardConfig = function () {
        return this.currentBoardConfig;
    };
    /**
     * 处理点击响应，根据服务器返回的结果更新棋盘 - 参考联机版的地图更新逻辑
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param result 点击结果 ("boom" | "safe" | number)
     */
    SingleChessBoardController.prototype.handleClickResponse = function (x, y, result) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u5904\u7406\u70B9\u51FB\u54CD\u5E94\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 如果格子上有biaoji预制体，先移除它
        if (this.hasBiaojiAt(x, y)) {
            // 直接移除，不播放动画
            var gridData = this.gridData[x][y];
            if (gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
            }
        }
        // 标记格子已被处理，防止重复点击
        this.gridData[x][y].hasPlayer = true;
        // 使用连锁动画的方式处理单个格子，保持一致性
        this.playGridDisappearAnimation(x, y, result);
    };
    /**
     * 处理连锁展开结果
     * @param floodFillResults 连锁展开数据数组
     */
    SingleChessBoardController.prototype.handleFloodFillResults = function (floodFillResults) {
        var _this = this;
        // 按延迟时间分组处理，创建连锁展开动画效果
        floodFillResults.forEach(function (gridResult, index) {
            var x = gridResult.x, y = gridResult.y, neighborMines = gridResult.neighborMines;
            if (!_this.isValidCoordinate(x, y)) {
                console.warn("\u8FDE\u9501\u5C55\u5F00\u8DF3\u8FC7\u65E0\u6548\u5750\u6807: (" + x + ", " + y + ")");
                return;
            }
            // 计算延迟时间，创建波浪式展开效果
            var delay = index * 0.05; // 每个格子延迟50毫秒
            _this.scheduleOnce(function () {
                // 使用连锁动画的方式处理格子，保持一致性
                _this.playGridDisappearAnimation(x, y, neighborMines);
            }, delay);
        });
    };
    /**
     * 批量处理连锁反应的格子（参考联机版的processFloodFillResult）
     * @param revealedGrids 被揭开的格子列表 {x: number, y: number, neighborMines: number}[]
     */
    SingleChessBoardController.prototype.handleChainReaction = function (revealedGrids) {
        var _this = this;
        if (!revealedGrids || revealedGrids.length === 0) {
            return;
        }
        // 为每个连锁格子播放消失动画，参考联机版的逻辑
        revealedGrids.forEach(function (block, index) {
            // 延迟播放动画，创造连锁效果
            _this.scheduleOnce(function () {
                _this.playGridDisappearAnimation(block.x, block.y, block.neighborMines);
            }, index * 0.1); // 每个格子间隔0.1秒
        });
    };
    /**
     * 播放格子消失动画（连锁效果）- 参考联机版ChessBoardController
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量或结果类型（可以是数字、"mine"、"boom"等）
     */
    SingleChessBoardController.prototype.playGridDisappearAnimation = function (x, y, neighborMines) {
        var _this = this;
        // 如果格子上有biaoji预制体，先移除它（连锁展开时）
        if (this.hasBiaojiAt(x, y)) {
            var gridData = this.gridData[x][y];
            if (gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
            }
        }
        // 标记格子已被处理（对于连锁格子）
        if (this.isValidCoordinate(x, y)) {
            this.gridData[x][y].hasPlayer = true;
        }
        // 先删除格子
        this.removeGridAt(x, y);
        // 延迟0.3秒后显示数字（等格子消失动画完成）
        // 使用带标识的延迟任务，方便重置时清理
        var delayCallback = function () {
            _this.updateNeighborMinesDisplay(x, y, neighborMines);
        };
        this.scheduleOnce(delayCallback, 0.3);
    };
    /**
     * 隐藏指定位置的格子（不销毁，以便重置时可以重新显示）- 参考联机版
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    SingleChessBoardController.prototype.removeGridAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            // 使用cc.Tween播放消失动画后隐藏（不销毁）
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridNode.active = false; // 隐藏而不是销毁
            })
                .start();
        }
    };
    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）- 参考联机版
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量或结果类型
     */
    SingleChessBoardController.prototype.updateNeighborMinesDisplay = function (x, y, neighborMines) {
        if (neighborMines === "boom" || neighborMines === "mine") {
            // 踩到地雷，生成boom预制体并触发震动
            this.createBoomPrefab(x, y, true); // true表示是当前用户踩到的雷，需要震动
        }
        else if (typeof neighborMines === "number" && neighborMines > 0) {
            // 显示数字
            this.createNumberPrefab(x, y, neighborMines);
        }
        else {
            // 如果是0、"safe"或其他，则不显示任何预制体
        }
    };
    /**
     * 移除指定位置的biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    SingleChessBoardController.prototype.removeBiaojiAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        var gridData = this.gridData[x][y];
        if (gridData.hasPlayer && gridData.playerNode && gridData.playerNode.name === "Biaoji") {
            // 播放消失动画
            cc.tween(gridData.playerNode)
                .to(0.2, { scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridData.playerNode.removeFromParent();
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            })
                .start();
        }
    };
    /**
     * 检查指定位置是否有biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 是否有biaoji预制体
     */
    SingleChessBoardController.prototype.hasBiaojiAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            return false;
        }
        var gridData = this.gridData[x][y];
        return gridData.hasPlayer && gridData.playerNode && gridData.playerNode.name === "Biaoji";
    };
    /**
     * 获取所有biaoji的位置
     * @returns biaoji位置数组
     */
    SingleChessBoardController.prototype.getAllBiaojiPositions = function () {
        var positions = [];
        if (!this.currentBoardConfig)
            return positions;
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                if (this.hasBiaojiAt(x, y)) {
                    positions.push({ x: x, y: y });
                }
            }
        }
        return positions;
    };
    /**
     * 重置棋盘到初始状态
     */
    SingleChessBoardController.prototype.resetBoard = function () {
        var _this = this;
        // 清理所有延迟任务（重要：防止上一局的连锁动画影响新游戏）
        this.unscheduleAllCallbacks();
        // 清除所有预制体
        this.clearAllPrefabs();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
        // 显示所有格子
        this.showAllHiddenGrids();
        // 重新启用触摸事件
        this.scheduleOnce(function () {
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    /**
     * 禁用所有格子的触摸事件（游戏结束时调用）
     */
    SingleChessBoardController.prototype.disableAllGridTouch = function () {
        if (!this.currentBoardConfig)
            return;
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
                if (gridNode) {
                    gridNode.off(cc.Node.EventType.TOUCH_START);
                    gridNode.off(cc.Node.EventType.TOUCH_END);
                    gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
                }
            }
        }
    };
    /**
     * 启用所有格子的触摸事件
     */
    SingleChessBoardController.prototype.enableAllGridTouch = function () {
        this.enableTouchForExistingGrids();
    };
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan10x10Node", void 0);
    SingleChessBoardController = __decorate([
        ccclass
    ], SingleChessBoardController);
    return SingleChessBoardController;
}(cc.Component));
exports.default = SingleChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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