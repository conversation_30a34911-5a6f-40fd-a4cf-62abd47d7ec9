
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/AIManagedDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '423f58DLGJCFIMOAtc3Vt+Q', 'AIManagedDialogController');
// scripts/game/AIManagedDialogController.ts

"use strict";
// AI托管中页面控制器
// 当玩家进入AI托管状态时显示，点击屏幕任何位置发送取消托管消息
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var Config_1 = require("../util/Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var AIManagedDialogController = /** @class */ (function (_super) {
    __extends(AIManagedDialogController, _super);
    function AIManagedDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null; // 背景板
        _this.maskNode = null; // 遮罩节点，用于接收点击事件
        _this.isShowing = false; // 是否正在显示
        return _this;
    }
    AIManagedDialogController.prototype.start = function () {
        // 初始化时隐藏
        this.node.active = false;
        // 为遮罩节点添加点击事件监听
        if (this.maskNode) {
            this.maskNode.on(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);
        }
    };
    /**
     * 显示托管中页面
     */
    AIManagedDialogController.prototype.show = function () {
        if (this.isShowing) {
            return;
        }
        this.isShowing = true;
        this.node.active = true;
        // 初始化动画状态
        if (this.boardBg) {
            this.boardBg.scale = 0;
            this.boardBg.opacity = 0;
        }
        // 执行显示动画
        this.playShowAnimation();
    };
    /**
     * 隐藏托管中页面
     */
    AIManagedDialogController.prototype.hide = function () {
        var _this = this;
        if (!this.isShowing) {
            return;
        }
        this.isShowing = false;
        // 执行隐藏动画
        this.playHideAnimation(function () {
            _this.node.active = false;
        });
    };
    /**
     * 播放显示动画
     */
    AIManagedDialogController.prototype.playShowAnimation = function () {
        if (!this.boardBg) {
            return;
        }
        // 缩放和透明度动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, {
            scale: 1,
            opacity: 255
        }, {
            easing: 'backOut'
        })
            .start();
    };
    /**
     * 播放隐藏动画
     * @param callback 动画完成回调
     */
    AIManagedDialogController.prototype.playHideAnimation = function (callback) {
        if (!this.boardBg) {
            if (callback)
                callback();
            return;
        }
        // 缩放和透明度动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, {
            scale: 0,
            opacity: 0
        }, {
            easing: 'backIn'
        })
            .call(function () {
            if (callback)
                callback();
        })
            .start();
    };
    /**
     * 遮罩点击事件处理
     */
    AIManagedDialogController.prototype.onMaskClick = function () {
        if (!this.isShowing) {
            return;
        }
        console.log("点击屏幕，发送取消AI托管消息");
        // 发送取消AI托管消息
        this.sendCancelAIManagement();
        // 立即隐藏页面（不等待服务器响应）
        this.hide();
    };
    /**
     * 发送取消AI托管消息
     */
    AIManagedDialogController.prototype.sendCancelAIManagement = function () {
        var cancelData = {
        // 可以根据需要添加其他参数
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeCancelAIManagement, cancelData);
        console.log("已发送取消AI托管消息");
    };
    /**
     * 检查是否正在显示
     */
    AIManagedDialogController.prototype.isVisible = function () {
        return this.isShowing && this.node.active;
    };
    AIManagedDialogController.prototype.onDestroy = function () {
        // 清理事件监听
        if (this.maskNode) {
            this.maskNode.off(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], AIManagedDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], AIManagedDialogController.prototype, "maskNode", void 0);
    AIManagedDialogController = __decorate([
        ccclass
    ], AIManagedDialogController);
    return AIManagedDialogController;
}(cc.Component));
exports.default = AIManagedDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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