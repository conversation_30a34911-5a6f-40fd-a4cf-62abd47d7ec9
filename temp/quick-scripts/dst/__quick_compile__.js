
(function () {
var scripts = [{"deps":{"./joui18n-script/LocalizedSprite":1,"./joui18n-script/LocalizedLabel":2,"./assets/meshTools/Singleton":16,"./assets/meshTools/BaseSDK":7,"./assets/meshTools/tools/MeshSdkApi":80,"./assets/meshTools/tools/Publish":4,"./assets/meshTools/tools/MeshSdk":31,"./assets/scripts/TipsDialogController":22,"./assets/scripts/ToastController":17,"./assets/scripts/GlobalManagerController":28,"./assets/scripts/bean/GameBean":18,"./assets/scripts/bean/GlobalBean":3,"./assets/scripts/bean/LanguageType":19,"./assets/scripts/bean/EnumBean":20,"./assets/scripts/common/GameData":24,"./assets/scripts/common/GameMgr":21,"./assets/scripts/common/GameTools":30,"./assets/scripts/common/MineConsole":9,"./assets/scripts/common/EventCenter":27,"./assets/scripts/game/BtnController":25,"./assets/scripts/game/CongratsDialogController":23,"./assets/scripts/game/GamePageController":29,"./assets/scripts/game/GameScoreController":77,"./assets/scripts/game/AIManagedDialogController":26,"./assets/scripts/game/Chess/GridController":6,"./assets/scripts/game/Chess/HexChessBoardController":35,"./assets/scripts/game/Chess/SingleChessBoardController":65,"./assets/scripts/game/Chess/ChessBoardController":32,"./assets/scripts/hall/HallCenterLayController":54,"./assets/scripts/hall/HallCreateRoomController":37,"./assets/scripts/hall/HallJoinRoomController":34,"./assets/scripts/hall/HallPageController":50,"./assets/scripts/hall/HallParentController":33,"./assets/scripts/hall/InfoDialogController":38,"./assets/scripts/hall/KickOutDialogController":40,"./assets/scripts/hall/LeaveDialogController":39,"./assets/scripts/hall/LevelSelectDemo":36,"./assets/scripts/hall/MatchParentController":42,"./assets/scripts/hall/PlayerLayoutController":43,"./assets/scripts/hall/SettingDialogController":41,"./assets/scripts/hall/TopUpDialogController":44,"./assets/scripts/hall/HallAutoController":45,"./assets/scripts/hall/Level/LevelSelectController":5,"./assets/scripts/hall/Level/LevelSelectExample":48,"./assets/scripts/hall/Level/LevelSelectPageController":46,"./assets/scripts/hall/Level/ScrollViewHelper":53,"./assets/scripts/hall/Level/LevelItemController":47,"./assets/scripts/level/LevelPageController":10,"./assets/scripts/net/GameServerUrl":11,"./assets/scripts/net/HttpManager":51,"./assets/scripts/net/HttpUtils":52,"./assets/scripts/net/IHttpMsgBody":49,"./assets/scripts/net/MessageBaseBean":56,"./assets/scripts/net/MessageId":55,"./assets/scripts/net/WebSocketManager":57,"./assets/scripts/net/WebSocketTool":59,"./assets/scripts/net/ErrorCode":60,"./assets/scripts/pfb/InfoItemController":12,"./assets/scripts/pfb/InfoItemOneController":58,"./assets/scripts/pfb/MatchItemController":63,"./assets/scripts/pfb/PlayerGameController ":61,"./assets/scripts/pfb/PlayerScoreController":62,"./assets/scripts/pfb/SeatItemController":66,"./assets/scripts/pfb/CongratsItemController":74,"./assets/scripts/start_up/StartUpPageController":13,"./assets/scripts/start_up/StartUpCenterController":67,"./assets/scripts/test/DebugShowMinesTest":81,"./assets/scripts/test/NoticeRoundStartTest":14,"./assets/scripts/test/AnimationTest":64,"./assets/scripts/util/AudioMgr":15,"./assets/scripts/util/BlockingQueue":75,"./assets/scripts/util/Config":79,"./assets/scripts/util/Dictionary":68,"./assets/scripts/util/LocalStorageManager":76,"./assets/scripts/util/NickNameLabel":69,"./assets/scripts/util/Tools":73,"./assets/scripts/util/AudioManager":70,"./assets/meshTools/MeshTools":71,"./assets/resources/i18n/zh_HK":78,"./assets/resources/i18n/en":8,"./assets/resources/i18n/zh_CN":72},"path":"preview-scripts/__qc_index__.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedSprite.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedLabel.js"},{"deps":{"../../meshTools/Singleton":16,"../hall/HallAutoController":45},"path":"preview-scripts/assets/scripts/bean/GlobalBean.js"},{"deps":{"../Singleton":16},"path":"preview-scripts/assets/meshTools/tools/Publish.js"},{"deps":{"./ScrollViewHelper":53},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/game/Chess/GridController.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/BaseSDK.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/en.js"},{"deps":{"../../meshTools/Singleton":16},"path":"preview-scripts/assets/scripts/common/MineConsole.js"},{"deps":{"../GlobalManagerController":28,"../hall/LeaveDialogController":39,"../util/Config":79,"../util/Tools":73,"../net/MessageId":55,"../net/WebSocketManager":57,"../common/EventCenter":27,"../common/GameMgr":21,"../game/Chess/SingleChessBoardController":65},"path":"preview-scripts/assets/scripts/level/LevelPageController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/GameServerUrl.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemController.js"},{"deps":{"../common/GameMgr":21,"./StartUpCenterController":67},"path":"preview-scripts/assets/scripts/start_up/StartUpPageController.js"},{"deps":{"../common/GameMgr":21,"../common/EventCenter":27,"../net/MessageId":55,"../bean/GlobalBean":3},"path":"preview-scripts/assets/scripts/test/NoticeRoundStartTest.js"},{"deps":{"./Config":79,"./Dictionary":68},"path":"preview-scripts/assets/scripts/util/AudioMgr.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/Singleton.js"},{"deps":{},"path":"preview-scripts/assets/scripts/ToastController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/GameBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/LanguageType.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/EnumBean.js"},{"deps":{"../../meshTools/tools/MeshSdkApi":80,"./EventCenter":27,"./GameData":24,"./GameTools":30,"./MineConsole":9},"path":"preview-scripts/assets/scripts/common/GameMgr.js"},{"deps":{"./util/Config":79,"./util/Tools":73},"path":"preview-scripts/assets/scripts/TipsDialogController.js"},{"deps":{"../bean/GlobalBean":3,"../common/EventCenter":27,"../common/GameMgr":21,"../net/MessageBaseBean":56,"../pfb/CongratsItemController":74,"../util/Config":79,"../util/Tools":73},"path":"preview-scripts/assets/scripts/game/CongratsDialogController.js"},{"deps":{"../../meshTools/MeshTools":71,"../../meshTools/Singleton":16,"../net/GameServerUrl":11},"path":"preview-scripts/assets/scripts/common/GameData.js"},{"deps":{"../util/AudioManager":70,"../util/Config":79,"../util/LocalStorageManager":76},"path":"preview-scripts/assets/scripts/game/BtnController.js"},{"deps":{"../net/WebSocketManager":57,"../net/MessageId":55,"../util/Config":79},"path":"preview-scripts/assets/scripts/game/AIManagedDialogController.js"},{"deps":{"../../meshTools/Singleton":16,"./GameMgr":21},"path":"preview-scripts/assets/scripts/common/EventCenter.js"},{"deps":{"./TipsDialogController":22,"./ToastController":17,"../meshTools/MeshTools":71,"../meshTools/tools/Publish":4,"./bean/GlobalBean":3,"./bean/LanguageType":19,"./bean/EnumBean":20,"./common/GameMgr":21,"./common/EventCenter":27,"./game/GamePageController":29,"./hall/TopUpDialogController":44,"./hall/HallPageController":50,"./level/LevelPageController":10,"./net/GameServerUrl":11,"./net/MessageBaseBean":56,"./net/MessageId":55,"./net/WebSocketManager":57,"./net/WebSocketTool":59,"./net/ErrorCode":60,"./start_up/StartUpPageController":13,"./util/Config":79,"./util/AudioMgr":15},"path":"preview-scripts/assets/scripts/GlobalManagerController.js"},{"deps":{"../bean/GlobalBean":3,"../hall/LeaveDialogController":39,"../util/AudioManager":70,"../util/Config":79,"../util/Tools":73,"./CongratsDialogController":23,"./GameScoreController":77,"./Chess/ChessBoardController":32,"./Chess/HexChessBoardController":35,"../pfb/PlayerGameController ":61,"../net/WebSocketManager":57,"../net/MessageId":55,"./AIManagedDialogController":26},"path":"preview-scripts/assets/scripts/game/GamePageController.js"},{"deps":{"../../meshTools/Singleton":16},"path":"preview-scripts/assets/scripts/common/GameTools.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/tools/MeshSdk.js"},{"deps":{"../../bean/GlobalBean":3,"../../pfb/PlayerGameController ":61},"path":"preview-scripts/assets/scripts/game/Chess/ChessBoardController.js"},{"deps":{"../../meshTools/tools/Publish":4,"../bean/GlobalBean":3,"../common/GameMgr":21,"../net/MessageId":55,"../net/WebSocketManager":57,"../ToastController":17,"../util/Config":79,"../util/Tools":73,"./HallCenterLayController":54},"path":"preview-scripts/assets/scripts/hall/HallParentController.js"},{"deps":{"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/HallJoinRoomController.js"},{"deps":{"../../bean/GlobalBean":3,"../../pfb/PlayerGameController ":61},"path":"preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js"},{"deps":{"./Level/LevelSelectController":5},"path":"preview-scripts/assets/scripts/hall/LevelSelectDemo.js"},{"deps":{"../bean/GlobalBean":3,"../pfb/SeatItemController":66,"../util/Config":79,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/HallCreateRoomController.js"},{"deps":{"../util/Config":79,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/InfoDialogController.js"},{"deps":{"../common/GameMgr":21,"../net/MessageId":55,"../net/WebSocketManager":57,"../util/Config":79,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/LeaveDialogController.js"},{"deps":{"../net/MessageId":55,"../net/WebSocketManager":57,"../util/Config":79,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/KickOutDialogController.js"},{"deps":{"../../meshTools/tools/Publish":4,"../util/AudioManager":70,"../util/Config":79,"../util/LocalStorageManager":76,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/SettingDialogController.js"},{"deps":{"../../meshTools/tools/Publish":4,"../bean/GlobalBean":3,"../common/EventCenter":27,"../common/GameMgr":21,"../net/MessageBaseBean":56,"../pfb/MatchItemController":63,"../util/Config":79,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/MatchParentController.js"},{"deps":{"../bean/GlobalBean":3,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/PlayerLayoutController.js"},{"deps":{"../common/GameMgr":21,"../util/Config":79,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/TopUpDialogController.js"},{"deps":{"../bean/GlobalBean":3,"../util/Config":79,"../util/Tools":73},"path":"preview-scripts/assets/scripts/hall/HallAutoController.js"},{"deps":{"../../GlobalManagerController":28,"./LevelSelectController":5,"../../net/MessageId":55,"../../net/WebSocketManager":57},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js"},{"deps":{"./LevelSelectController":5},"path":"preview-scripts/assets/scripts/hall/Level/LevelItemController.js"},{"deps":{"./LevelSelectController":5},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectExample.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/IHttpMsgBody.js"},{"deps":{"../bean/GlobalBean":3,"../common/GameMgr":21,"../net/MessageId":55,"../net/WebSocketManager":57,"../net/WebSocketTool":59,"../ToastController":17,"../util/AudioManager":70,"./HallParentController":33,"./InfoDialogController":38,"./KickOutDialogController":40,"./LeaveDialogController":39,"./Level/LevelSelectPageController":46,"./MatchParentController":42,"./SettingDialogController":41},"path":"preview-scripts/assets/scripts/hall/HallPageController.js"},{"deps":{"./HttpUtils":52,"./MessageBaseBean":56,"./GameServerUrl":11,"../../meshTools/MeshTools":71,"../common/GameMgr":21,"../common/EventCenter":27},"path":"preview-scripts/assets/scripts/net/HttpManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/HttpUtils.js"},{"deps":{},"path":"preview-scripts/assets/scripts/hall/Level/ScrollViewHelper.js"},{"deps":{"../bean/GlobalBean":3,"../net/MessageId":55,"../net/WebSocketManager":57,"../ToastController":17,"./HallAutoController":45,"./HallCreateRoomController":37,"./HallJoinRoomController":34},"path":"preview-scripts/assets/scripts/hall/HallCenterLayController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageId.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageBaseBean.js"},{"deps":{"../../meshTools/Singleton":16,"../common/EventCenter":27,"../common/GameMgr":21,"./WebSocketTool":59},"path":"preview-scripts/assets/scripts/net/WebSocketManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemOneController.js"},{"deps":{"./MessageBaseBean":56,"./MessageId":55,"../util/Tools":73,"../../meshTools/Singleton":16,"../common/EventCenter":27,"../common/GameMgr":21},"path":"preview-scripts/assets/scripts/net/WebSocketTool.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/ErrorCode.js"},{"deps":{"../util/Tools":73},"path":"preview-scripts/assets/scripts/pfb/PlayerGameController .js"},{"deps":{"../bean/GlobalBean":3,"../util/NickNameLabel":69,"../util/Tools":73},"path":"preview-scripts/assets/scripts/pfb/PlayerScoreController.js"},{"deps":{"../util/NickNameLabel":69,"../util/Tools":73},"path":"preview-scripts/assets/scripts/pfb/MatchItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/test/AnimationTest.js"},{"deps":{"../../net/WebSocketManager":57,"../../net/MessageId":55},"path":"preview-scripts/assets/scripts/game/Chess/SingleChessBoardController.js"},{"deps":{"../util/NickNameLabel":69,"../util/Tools":73},"path":"preview-scripts/assets/scripts/pfb/SeatItemController.js"},{"deps":{"../common/EventCenter":27,"../common/GameMgr":21,"../net/MessageBaseBean":56,"../util/Config":79},"path":"preview-scripts/assets/scripts/start_up/StartUpCenterController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Dictionary.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/NickNameLabel.js"},{"deps":{"./AudioMgr":15,"./LocalStorageManager":76},"path":"preview-scripts/assets/scripts/util/AudioManager.js"},{"deps":{"./tools/Publish":4},"path":"preview-scripts/assets/meshTools/MeshTools.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_CN.js"},{"deps":{"./AudioManager":70,"./Config":79},"path":"preview-scripts/assets/scripts/util/Tools.js"},{"deps":{"../../meshTools/tools/Publish":4,"../util/Config":79,"../util/NickNameLabel":69,"../util/Tools":73},"path":"preview-scripts/assets/scripts/pfb/CongratsItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/BlockingQueue.js"},{"deps":{"../../meshTools/Singleton":16},"path":"preview-scripts/assets/scripts/util/LocalStorageManager.js"},{"deps":{"../bean/GlobalBean":3,"../pfb/PlayerScoreController":62},"path":"preview-scripts/assets/scripts/game/GameScoreController.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_HK.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Config.js"},{"deps":{"../MeshTools":71,"../BaseSDK":7,"../../scripts/net/MessageBaseBean":56,"../../scripts/common/GameMgr":21,"../../scripts/common/EventCenter":27,"MeshSdk":31},"path":"preview-scripts/assets/meshTools/tools/MeshSdkApi.js"},{"deps":{"../net/MessageId":55,"../net/WebSocketManager":57,"../common/GameMgr":21,"../common/EventCenter":27},"path":"preview-scripts/assets/scripts/test/DebugShowMinesTest.js"}];
var entries = ["preview-scripts/__qc_index__.js"];
var bundleScript = 'preview-scripts/__qc_bundle__.js';

/**
 * Notice: This file can not use ES6 (for IE 11)
 */
var modules = {};
var name2path = {};

// Will generated by module.js plugin
// var scripts = ${scripts};
// var entries = ${entries};
// var bundleScript = ${bundleScript};

if (typeof global === 'undefined') {
    window.global = window;
}

var isJSB = typeof jsb !== 'undefined';

function getXMLHttpRequest () {
    return window.XMLHttpRequest ? new window.XMLHttpRequest() : new ActiveXObject('MSXML2.XMLHTTP');
}

function downloadText(url, callback) {
    if (isJSB) {
        var result = jsb.fileUtils.getStringFromFile(url);
        callback(null, result);
        return;
    }

    var xhr = getXMLHttpRequest(),
        errInfo = 'Load text file failed: ' + url;
    xhr.open('GET', url, true);
    if (xhr.overrideMimeType) xhr.overrideMimeType('text\/plain; charset=utf-8');
    xhr.onload = function () {
        if (xhr.readyState === 4) {
            if (xhr.status === 200 || xhr.status === 0) {
                callback(null, xhr.responseText);
            }
            else {
                callback({status:xhr.status, errorMessage:errInfo + ', status: ' + xhr.status});
            }
        }
        else {
            callback({status:xhr.status, errorMessage:errInfo + '(wrong readyState)'});
        }
    };
    xhr.onerror = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(error)'});
    };
    xhr.ontimeout = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(time out)'});
    };
    xhr.send(null);
};

function loadScript (src, cb) {
    if (typeof require !== 'undefined') {
        require(src);
        return cb();
    }

    // var timer = 'load ' + src;
    // console.time(timer);

    var scriptElement = document.createElement('script');

    function done() {
        // console.timeEnd(timer);
        // deallocation immediate whatever
        scriptElement.remove();
    }

    scriptElement.onload = function () {
        done();
        cb();
    };
    scriptElement.onerror = function () {
        done();
        var error = 'Failed to load ' + src;
        console.error(error);
        cb(new Error(error));
    };
    scriptElement.setAttribute('type','text/javascript');
    scriptElement.setAttribute('charset', 'utf-8');
    scriptElement.setAttribute('src', src);

    document.head.appendChild(scriptElement);
}

function loadScripts (srcs, cb) {
    var n = srcs.length;

    srcs.forEach(function (src) {
        loadScript(src, function () {
            n--;
            if (n === 0) {
                cb();
            }
        });
    })
}

function formatPath (path) {
    let destPath = window.__quick_compile_project__.destPath;
    if (destPath) {
        let prefix = 'preview-scripts';
        if (destPath[destPath.length - 1] === '/') {
            prefix += '/';
        }
        path = path.replace(prefix, destPath);
    }
    return path;
}

window.__quick_compile_project__ = {
    destPath: '',

    registerModule: function (path, module) {
        path = formatPath(path);
        modules[path].module = module;
    },

    registerModuleFunc: function (path, func) {
        path = formatPath(path);
        modules[path].func = func;

        var sections = path.split('/');
        var name = sections[sections.length - 1];
        name = name.replace(/\.(?:js|ts|json)$/i, '');
        name2path[name] = path;
    },

    require: function (request, path) {
        var m, requestScript;

        path = formatPath(path);
        if (path) {
            m = modules[path];
            if (!m) {
                console.warn('Can not find module for path : ' + path);
                return null;
            }
        }

        if (m) {
            let depIndex = m.deps[request];
            // dependence script was excluded
            if (depIndex === -1) {
                return null;
            }
            else {
                requestScript = scripts[ m.deps[request] ];
            }
        }
        
        let requestPath = '';
        if (!requestScript) {
            // search from name2path when request is a dynamic module name
            if (/^[\w- .]*$/.test(request)) {
                requestPath = name2path[request];
            }

            if (!requestPath) {
                if (CC_JSB) {
                    return require(request);
                }
                else {
                    console.warn('Can not find deps [' + request + '] for path : ' + path);
                    return null;
                }
            }
        }
        else {
            requestPath = formatPath(requestScript.path);
        }

        let requestModule = modules[requestPath];
        if (!requestModule) {
            console.warn('Can not find request module for path : ' + requestPath);
            return null;
        }

        if (!requestModule.module && requestModule.func) {
            requestModule.func();
        }

        if (!requestModule.module) {
            console.warn('Can not find requestModule.module for path : ' + path);
            return null;
        }

        return requestModule.module.exports;
    },

    run: function () {
        entries.forEach(function (entry) {
            entry = formatPath(entry);
            var module = modules[entry];
            if (!module.module) {
                module.func();
            }
        });
    },

    load: function (cb) {
        var self = this;

        var srcs = scripts.map(function (script) {
            var path = formatPath(script.path);
            modules[path] = script;

            if (script.mtime) {
                path += ("?mtime=" + script.mtime);
            }
            return path;
        });

        console.time && console.time('load __quick_compile_project__');
        // jsb can not analysis sourcemap, so keep separate files.
        if (bundleScript && !isJSB) {
            downloadText(formatPath(bundleScript), function (err, bundleSource) {
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                if (err) {
                    console.error(err);
                    return;
                }

                let evalTime = 'eval __quick_compile_project__ : ' + srcs.length + ' files';
                console.time && console.time(evalTime);
                var sources = bundleSource.split('\n//------QC-SOURCE-SPLIT------\n');
                for (var i = 0; i < sources.length; i++) {
                    if (sources[i]) {
                        window.eval(sources[i]);
                        // not sure why new Function cannot set breakpoints precisely
                        // new Function(sources[i])()
                    }
                }
                self.run();
                console.timeEnd && console.timeEnd(evalTime);
                cb();
            })
        }
        else {
            loadScripts(srcs, function () {
                self.run();
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                cb();
            });
        }
    }
};

// Polyfill for IE 11
if (!('remove' in Element.prototype)) {
    Element.prototype.remove = function () {
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    };
}
})();
    