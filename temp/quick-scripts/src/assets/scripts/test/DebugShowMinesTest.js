"use strict";
cc._RF.push(module, '4d26ae/B4BKRLFRxcgY94Mp', 'DebugShowMinesTest');
// scripts/test/DebugShowMinesTest.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var DebugShowMinesTest = /** @class */ (function (_super) {
    __extends(DebugShowMinesTest, _super);
    function DebugShowMinesTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.testButton = null;
        _this.statusLabel = null;
        return _this;
    }
    DebugShowMinesTest.prototype.start = function () {
        if (this.testButton) {
            this.testButton.node.on('click', this.sendDebugShowMinesMessage, this);
        }
        if (this.statusLabel) {
            this.statusLabel.string = '点击按钮测试DebugShowMines消息';
        }
    };
    // 发送测试的DebugShowMines消息
    DebugShowMinesTest.prototype.sendDebugShowMinesMessage = function () {
        var _this = this;
        cc.log("发送DebugShowMines测试消息");
        // 发送DebugShowMines消息到服务器
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeDebugShowMines, {});
        // 模拟服务器响应（用于测试）
        this.scheduleOnce(function () {
            _this.simulateServerResponse();
        }, 1.0);
        if (this.statusLabel) {
            this.statusLabel.string = '已发送DebugShowMines消息';
        }
    };
    // 模拟服务器响应
    DebugShowMinesTest.prototype.simulateServerResponse = function () {
        // 创建模拟的地雷位置数据
        var testMinePositions = [
            { x: 1, y: 1 },
            { x: 3, y: 2 },
            { x: 5, y: 4 },
            { x: 2, y: 6 },
            { x: 7, y: 3 }
        ];
        // 模拟接收到的消息格式
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeDebugShowMines,
            code: 0,
            msg: "success",
            data: testMinePositions
        };
        // 发送消息事件
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u6A21\u62DF\u54CD\u5E94\uFF1A\u663E\u793A" + testMinePositions.length + "\u4E2A\u5730\u96F7\u4F4D\u7F6E";
        }
    };
    DebugShowMinesTest.prototype.onDestroy = function () {
        if (this.testButton) {
            this.testButton.node.off('click', this.sendDebugShowMinesMessage, this);
        }
    };
    __decorate([
        property(cc.Button)
    ], DebugShowMinesTest.prototype, "testButton", void 0);
    __decorate([
        property(cc.Label)
    ], DebugShowMinesTest.prototype, "statusLabel", void 0);
    DebugShowMinesTest = __decorate([
        ccclass
    ], DebugShowMinesTest);
    return DebugShowMinesTest;
}(cc.Component));
exports.default = DebugShowMinesTest;

cc._RF.pop();