"use strict";
cc._RF.push(module, 'c8e13uMxpxGELez69T2SfJx', 'LevelPageController');
// scripts/level/LevelPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var Tools_1 = require("../util/Tools");
var Config_1 = require("../util/Config");
var GlobalManagerController_1 = require("../GlobalManagerController");
var SingleChessBoardController_1 = require("../game/Chess/SingleChessBoardController");
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelPageController = /** @class */ (function (_super) {
    __extends(LevelPageController, _super);
    function LevelPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 返回按钮
        _this.backButton = null;
        // 开始游戏按钮
        _this.startGameButton = null;
        // 地雷数UI标签
        _this.mineCountLabel = null;
        // 当前关卡数UI标签
        _this.currentLevelLabel = null;
        // 退出游戏弹窗
        _this.leaveDialogController = null;
        // level_page节点
        _this.levelPageNode = null;
        // game_map_1节点
        _this.gameMap1Node = null;
        // game_map_2节点
        _this.gameMap2Node = null;
        // 方形地图节点引用
        _this.qipan8x8Node = null; // level_page/game_map_1/chess_bg/qipan8*8
        _this.qipan8x9Node = null; // level_page/game_map_1/chess_bg/qipan8*9
        _this.qipan9x9Node = null; // level_page/game_map_1/chess_bg/qipan9*9
        _this.qipan9x10Node = null; // level_page/game_map_1/chess_bg/qipan9*10
        _this.qipan10x10Node = null; // level_page/game_map_1/chess_bg/qipan10*10
        // 特殊关卡节点引用
        _this.levelS001Node = null; // level_page/game_map_2/game_bg/Level_S001
        _this.levelS002Node = null; // level_page/game_map_2/game_bg/Level_S002
        _this.levelS003Node = null; // level_page/game_map_2/game_bg/Level_S003
        _this.levelS004Node = null; // level_page/game_map_2/game_bg/Level_S004
        _this.levelS005Node = null; // level_page/game_map_2/game_bg/Level_S005
        _this.levelS006Node = null; // level_page/game_map_2/game_bg/Level_S006
        // 单机模式棋盘控制器
        _this.singleChessBoardController = null;
        // 测试按钮（用于调试显示地雷位置）
        _this.debugShowMinesButton = null;
        // 测试预制体（用于显示地雷位置）
        _this.debugMinePrefab = null;
        // 结算页面相关节点
        _this.levelSettlementNode = null; // level_settlement节点
        _this.boardBgNode = null; // level_settlement/board_bg节点
        _this.loseBgNode = null; // level_settlement/board_bg/lose_bg节点
        _this.winBgNode = null; // level_settlement/board_bg/win_bg节点
        _this.retryButton = null; // 再来一次按钮
        _this.nextLevelButton = null; // 下一关按钮
        // 当前关卡数据
        _this.currentLevel = 1;
        _this.currentLevelInfo = null;
        _this.currentRoomId = 0; // 当前关卡游戏的房间ID
        _this.currentSingleChessBoard = null; // 当前激活的单机棋盘
        // 性能优化相关
        _this.lastShownMapNode = null; // 记录上次显示的地图节点
        _this.isUpdating = false; // 防止重复更新
        return _this;
    }
    LevelPageController.prototype.onLoad = function () {
        var _this = this;
        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式
        if (this.backButton) {
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        // 注册单机模式消息监听
        this.registerSingleModeMessageHandlers();
        // 设置结算页面按钮事件
        this.setupSettlementButtons();
        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新
    };
    LevelPageController.prototype.start = function () {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();
        // 设置测试按钮点击事件
        this.setupDebugButton();
    };
    /**
     * 返回按钮点击事件
     */
    LevelPageController.prototype.onBackButtonClick = function () {
        // 如果没有有效的房间ID，说明还没有进入游戏，直接返回关卡选择页面
        if (this.currentRoomId <= 0) {
            this.returnToLevelSelect();
            return;
        }
        // 弹出确认退出对话框，type=1表示退出本局游戏，传递当前房间ID
        if (this.leaveDialogController) {
            this.leaveDialogController.show(1, function () {
            }, this.currentRoomId);
        }
        else {
            cc.warn("LeaveDialogController 未配置");
        }
    };
    /**
     * 返回到关卡选择页面
     */
    LevelPageController.prototype.returnToLevelSelect = function () {
        // 查找GlobalManagerController并切换到大厅页面
        var globalManagerNode = cc.find("Canvas/global_node") || cc.find("global_node");
        if (globalManagerNode) {
            var globalManager = globalManagerNode.getComponent(GlobalManagerController_1.default);
            if (globalManager) {
                globalManager.setCurrentPage(GlobalManagerController_1.PageType.HALL_PAGE);
            }
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelPageController.prototype.onStartGameButtonClick = function () {
        // ExtendLevelInfo消息现在由LevelSelectPageController发送
        // 这里直接进入游戏，等待后端响应
    };
    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    LevelPageController.prototype.onExtendLevelInfo = function (levelInfo) {
        this.currentLevelInfo = levelInfo;
        // 保存房间ID，用于退出时使用
        if (levelInfo.roomId) {
            this.currentRoomId = levelInfo.roomId;
        }
        // 开始新游戏时，先重置当前棋盘（清理上一局的痕迹）
        if (this.currentSingleChessBoard) {
            this.currentSingleChessBoard.resetBoard();
        }
        // 更新地雷数UI
        this.updateMineCountUI(levelInfo.mineCount);
        // 使用当前设置的关卡编号，而不是后端返回的levelId
        // 因为后端的levelId可能与前端的关卡编号不一致
        this.enterLevel(this.currentLevel);
        // 通知当前激活的单机棋盘控制器处理ExtendLevelInfo
        if (this.currentSingleChessBoard) {
            this.currentSingleChessBoard.onExtendLevelInfo();
        }
    };
    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    LevelPageController.prototype.updateMineCountUI = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
        }
    };
    /**
     * 更新当前关卡数UI
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.updateCurrentLevelUI = function (levelNumber) {
        if (this.currentLevelLabel) {
            this.currentLevelLabel.string = "\u7B2C" + levelNumber + "\u5173";
        }
    };
    /**
     * 根据关卡数进入相应的关卡（优化版本）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.enterLevel = function (levelNumber) {
        // 防止重复更新
        if (this.isUpdating) {
            return;
        }
        this.isUpdating = true;
        // 更新关卡数UI显示
        this.updateCurrentLevelUI(levelNumber);
        // 获取目标地图节点和容器
        var targetMapInfo = this.getMapNodeByLevel(levelNumber);
        if (!targetMapInfo) {
            cc.warn("\u672A\u77E5\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
            this.isUpdating = false;
            return;
        }
        // 只有当目标节点与当前显示的节点不同时才进行切换
        if (this.lastShownMapNode !== targetMapInfo.mapNode) {
            // 隐藏上一个显示的地图节点
            if (this.lastShownMapNode) {
                this.lastShownMapNode.active = false;
            }
            // 显示目标容器和地图节点
            this.showMapContainer(targetMapInfo.containerType);
            this.showMapNodeOptimized(targetMapInfo.mapNode, targetMapInfo.mapName);
            // 记录当前显示的节点
            this.lastShownMapNode = targetMapInfo.mapNode;
        }
        // 设置当前激活的单机棋盘控制器
        if (this.singleChessBoardController) {
            // 根据关卡设置棋盘类型
            var boardType = this.getBoardTypeByLevel(levelNumber);
            this.singleChessBoardController.initBoard(boardType);
            this.currentSingleChessBoard = this.singleChessBoardController;
        }
        else {
            this.currentSingleChessBoard = null;
        }
        this.isUpdating = false;
    };
    /**
     * 根据关卡数获取对应的地图节点信息
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getMapNodeByLevel = function (levelNumber) {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return { mapNode: this.qipan8x8Node, mapName: "qipan8*8", containerType: 'map1' };
        }
        else if (levelNumber === 5) {
            return { mapNode: this.levelS001Node, mapName: "Level_S001", containerType: 'map2' };
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            return { mapNode: this.qipan8x9Node, mapName: "qipan8*9", containerType: 'map1' };
        }
        else if (levelNumber === 10) {
            return { mapNode: this.levelS002Node, mapName: "Level_S002", containerType: 'map2' };
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            return { mapNode: this.qipan9x9Node, mapName: "qipan9*9", containerType: 'map1' };
        }
        else if (levelNumber === 15) {
            return { mapNode: this.levelS003Node, mapName: "Level_S003", containerType: 'map2' };
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            return { mapNode: this.qipan9x10Node, mapName: "qipan9*10", containerType: 'map1' };
        }
        else if (levelNumber === 20) {
            return { mapNode: this.levelS004Node, mapName: "Level_S004", containerType: 'map2' };
        }
        else if (levelNumber >= 21 && levelNumber <= 24) {
            return { mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1' };
        }
        else if (levelNumber === 25) {
            return { mapNode: this.levelS005Node, mapName: "Level_S005", containerType: 'map2' };
        }
        else if (levelNumber >= 26 && levelNumber <= 29) {
            return { mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1' };
        }
        else if (levelNumber === 30) {
            return { mapNode: this.levelS006Node, mapName: "Level_S006", containerType: 'map2' };
        }
        return null;
    };
    /**
     * 根据关卡编号获取棋盘类型
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getBoardTypeByLevel = function (levelNumber) {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return "8x8";
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            return "8x9";
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            return "9x9";
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            return "9x10";
        }
        else if (levelNumber >= 21 && levelNumber <= 24 || levelNumber >= 26 && levelNumber <= 29) {
            return "10x10";
        }
        return "8x8"; // 默认
    };
    /**
     * 显示指定的地图节点（优化版本）
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    LevelPageController.prototype.showMapNodeOptimized = function (mapNode, mapName) {
        if (mapNode) {
            mapNode.active = true;
        }
        else {
            cc.warn("\u274C \u5730\u56FE\u8282\u70B9\u672A\u627E\u5230: " + mapName);
        }
    };
    /**
     * 显示指定的地图容器
     * @param containerType 容器类型
     */
    LevelPageController.prototype.showMapContainer = function (containerType) {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 根据容器类型显示对应容器，隐藏另一个
        if (containerType === 'map1') {
            if (this.gameMap1Node && !this.gameMap1Node.active) {
                this.gameMap1Node.active = true;
            }
            if (this.gameMap2Node && this.gameMap2Node.active) {
                this.gameMap2Node.active = false;
            }
        }
        else {
            if (this.gameMap2Node && !this.gameMap2Node.active) {
                this.gameMap2Node.active = true;
            }
            if (this.gameMap1Node && this.gameMap1Node.active) {
                this.gameMap1Node.active = false;
            }
        }
    };
    /**
     * 隐藏所有地图节点
     */
    LevelPageController.prototype.hideAllMapNodes = function () {
        var allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];
        allMapNodes.forEach(function (node) {
            if (node) {
                node.active = false;
            }
        });
    };
    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.setCurrentLevel = function (levelNumber) {
        this.currentLevel = levelNumber;
        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    };
    /**
     * 获取当前关卡编号
     */
    LevelPageController.prototype.getCurrentLevel = function () {
        return this.currentLevel;
    };
    /**
     * 获取当前关卡信息
     */
    LevelPageController.prototype.getCurrentLevelInfo = function () {
        return this.currentLevelInfo;
    };
    /**
     * 获取当前房间ID
     */
    LevelPageController.prototype.getCurrentRoomId = function () {
        return this.currentRoomId;
    };
    /**
     * 隐藏所有地图容器
     */
    LevelPageController.prototype.hideAllMapContainers = function () {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
        }
        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    };
    /**
     * 显示 game_map_1 容器（方形地图）
     */
    LevelPageController.prototype.showGameMap1 = function () {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
        }
        else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    };
    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    LevelPageController.prototype.showGameMap2 = function () {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;
        }
        else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    };
    /**
     * 获取当前激活的单机棋盘控制器
     */
    LevelPageController.prototype.getCurrentSingleChessBoard = function () {
        return this.currentSingleChessBoard;
    };
    /**
     * 处理单机模式的点击响应
     * @param response 点击响应数据
     */
    LevelPageController.prototype.handleSingleModeClickResponse = function (response) {
        if (this.currentSingleChessBoard) {
            var x = response.x, y = response.y, result = response.result, chainReaction = response.chainReaction;
            // 处理点击结果
            if (x !== undefined && y !== undefined && result !== undefined) {
                this.currentSingleChessBoard.handleClickResponse(x, y, result);
            }
            // 处理连锁反应
            if (chainReaction && Array.isArray(chainReaction)) {
                this.currentSingleChessBoard.handleChainReaction(chainReaction);
            }
        }
    };
    /**
     * 处理单机模式游戏结束
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.handleSingleModeGameEnd = function (gameEndData) {
        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();
            // 处理游戏结束
            this.currentSingleChessBoard.onLevelGameEnd();
        }
    };
    /**
     * 重置当前单机棋盘（仅在开始新游戏时调用）
     */
    LevelPageController.prototype.resetCurrentSingleChessBoard = function () {
        if (this.currentSingleChessBoard) {
            // 重置棋盘状态（清理所有预制体和格子状态）
            this.currentSingleChessBoard.resetBoard();
            // 重新启用触摸事件
            this.currentSingleChessBoard.enableAllGridTouch();
        }
    };
    /**
     * 注册单机模式消息处理器
     */
    LevelPageController.prototype.registerSingleModeMessageHandlers = function () {
        // 监听WebSocket消息
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 取消单机模式消息监听
     */
    LevelPageController.prototype.unregisterSingleModeMessageHandlers = function () {
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 处理接收到的WebSocket消息
     * @param messageBean 消息数据
     */
    LevelPageController.prototype.onReceiveMessage = function (messageBean) {
        switch (messageBean.msgId) {
            case MessageId_1.MessageId.MsgTypeLevelClickBlock:
                this.onLevelClickBlockResponse(messageBean.data);
                break;
            case MessageId_1.MessageId.MsgTypeLevelGameEnd:
                this.onLevelGameEnd(messageBean.data);
                break;
        }
    };
    /**
     * 处理LevelClickBlock响应
     * @param response 点击响应数据
     */
    LevelPageController.prototype.onLevelClickBlockResponse = function (response) {
        if (this.currentSingleChessBoard) {
            // 解构响应数据，支持多种可能的字段名
            var x = response.x, y = response.y, result = response.result, action = response.action, chainReaction = response.chainReaction, revealedGrids = response.revealedGrids, floodFill = response.floodFill, revealedBlocks = response.revealedBlocks, floodFillResults = response.floodFillResults // 单机模式使用这个字段
            ;
            // 根据action类型处理不同的响应
            if (x !== undefined && y !== undefined && result !== undefined) {
                if (action === 2) {
                    // 标记/取消标记操作，不调用handleClickResponse，避免格子消失
                    // 不调用 handleClickResponse，因为标记操作不应该隐藏格子
                }
                else if (action === 1) {
                    // 挖掘操作
                    this.currentSingleChessBoard.handleClickResponse(x, y, result);
                    // 处理连锁展开数据
                    if (floodFillResults && Array.isArray(floodFillResults) && floodFillResults.length > 0) {
                        this.currentSingleChessBoard.handleFloodFillResults(floodFillResults);
                    }
                }
                else {
                    // 其他操作，默认按挖掘处理
                    this.currentSingleChessBoard.handleClickResponse(x, y, result);
                }
            }
        }
    };
    /**
     * 处理LevelGameEnd通知
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.onLevelGameEnd = function (gameEndData) {
        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();
            // 处理游戏结束（不清理数据）
            this.currentSingleChessBoard.onLevelGameEnd();
        }
        // 显示结算页面
        this.showLevelSettlement(gameEndData);
    };
    /**
     * 设置结算页面按钮事件
     */
    LevelPageController.prototype.setupSettlementButtons = function () {
        // 再来一次按钮
        if (this.retryButton) {
            this.retryButton.node.on('click', this.onRetryButtonClick, this);
        }
        // 下一关按钮
        if (this.nextLevelButton) {
            this.nextLevelButton.node.on('click', this.onNextLevelButtonClick, this);
        }
    };
    /**
     * 显示结算页面
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.showLevelSettlement = function (gameEndData) {
        if (!this.levelSettlementNode) {
            console.error("levelSettlementNode 未配置");
            return;
        }
        // 显示结算页面
        this.levelSettlementNode.active = true;
        // 根据游戏结果显示对应的背景 - 修复成功判断逻辑
        var isSuccess = gameEndData.isSuccess || gameEndData.success || gameEndData.isWin || gameEndData.gameStatus === 1;
        if (isSuccess) {
            // 成功 - 显示胜利背景
            if (this.winBgNode) {
                this.winBgNode.active = true;
            }
            if (this.loseBgNode) {
                this.loseBgNode.active = false;
            }
        }
        else {
            // 失败 - 显示失败背景
            if (this.loseBgNode) {
                this.loseBgNode.active = true;
            }
            if (this.winBgNode) {
                this.winBgNode.active = false;
            }
        }
    };
    /**
     * 再来一次按钮点击事件
     */
    LevelPageController.prototype.onRetryButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹
        // 发送当前关卡的ExtendLevelInfo
        this.sendExtendLevelInfo(this.currentLevel);
    };
    /**
     * 下一关按钮点击事件
     */
    LevelPageController.prototype.onNextLevelButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 进入下一关
        var nextLevel = this.currentLevel + 1;
        this.setCurrentLevel(nextLevel);
        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹
        // 发送下一关的ExtendLevelInfo
        this.sendExtendLevelInfo(nextLevel);
    };
    /**
     * 隐藏结算页面
     */
    LevelPageController.prototype.hideLevelSettlement = function () {
        if (this.levelSettlementNode) {
            this.levelSettlementNode.active = false;
        }
    };
    /**
     * 发送ExtendLevelInfo消息
     * @param levelId 关卡ID
     */
    LevelPageController.prototype.sendExtendLevelInfo = function (levelId) {
        var request = {
            levelId: levelId
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelInfo, request);
    };
    /**
     * 设置测试按钮
     */
    LevelPageController.prototype.setupDebugButton = function () {
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.on('click', this.onDebugShowMinesClick, this);
        }
    };
    /**
     * 测试按钮点击事件 - 发送DebugShowMines消息
     */
    LevelPageController.prototype.onDebugShowMinesClick = function () {
        // 只在单机模式下工作
        if (!this.isInSingleMode()) {
            cc.warn("测试功能只在单机模式下可用");
            return;
        }
        cc.log("发送DebugShowMines消息");
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeDebugShowMines, {});
    };
    /**
     * 判断是否在单机模式
     */
    LevelPageController.prototype.isInSingleMode = function () {
        // 单机模式的判断逻辑：当前页面是关卡页面且有有效的房间ID
        return this.currentRoomId > 0;
    };
    /**
     * 处理DebugShowMines响应，在炸弹位置生成测试预制体
     * @param minePositions 炸弹位置数组 [{x: number, y: number}]
     */
    LevelPageController.prototype.handleDebugShowMines = function (minePositions) {
        var _this = this;
        if (!this.debugMinePrefab) {
            cc.warn("debugMinePrefab 预制体未设置，无法显示测试标记");
            return;
        }
        if (!this.currentSingleChessBoard) {
            cc.warn("当前没有激活的单机棋盘");
            return;
        }
        cc.log("收到地雷位置数据，开始生成测试预制体:", minePositions);
        // 在每个炸弹位置生成测试预制体
        minePositions.forEach(function (position, index) {
            _this.scheduleOnce(function () {
                _this.createDebugMinePrefab(position.x, position.y);
            }, index * 0.1); // 每个预制体间隔0.1秒生成，产生动画效果
        });
    };
    /**
     * 在指定位置创建测试预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    LevelPageController.prototype.createDebugMinePrefab = function (x, y) {
        if (!this.debugMinePrefab || !this.currentSingleChessBoard) {
            return;
        }
        // 使用棋盘控制器的公共方法创建自定义预制体
        var debugNode = this.currentSingleChessBoard.createCustomPrefab(x, y, this.debugMinePrefab, "DebugMine_" + x + "_" + y);
        cc.log("\u5728\u4F4D\u7F6E (" + x + ", " + y + ") \u521B\u5EFA\u4E86\u6D4B\u8BD5\u9884\u5236\u4F53");
    };
    LevelPageController.prototype.onDestroy = function () {
        // 取消消息监听
        this.unregisterSingleModeMessageHandlers();
        // 清理按钮事件
        if (this.retryButton) {
            this.retryButton.node.off('click', this.onRetryButtonClick, this);
        }
        if (this.nextLevelButton) {
            this.nextLevelButton.node.off('click', this.onNextLevelButtonClick, this);
        }
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.off('click', this.onDebugShowMinesClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "backButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "currentLevelLabel", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], LevelPageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelPageNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap1Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap2Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan10x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS001Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS002Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS003Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS004Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS005Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS006Node", void 0);
    __decorate([
        property(SingleChessBoardController_1.default)
    ], LevelPageController.prototype, "singleChessBoardController", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "debugShowMinesButton", void 0);
    __decorate([
        property(cc.Prefab)
    ], LevelPageController.prototype, "debugMinePrefab", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelSettlementNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "boardBgNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "loseBgNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "winBgNode", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "retryButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "nextLevelButton", void 0);
    LevelPageController = __decorate([
        ccclass
    ], LevelPageController);
    return LevelPageController;
}(cc.Component));
exports.default = LevelPageController;

cc._RF.pop();