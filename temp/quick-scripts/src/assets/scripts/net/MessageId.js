"use strict";
cc._RF.push(module, 'f1db1fNQYxAjIe8lTfdT1iF', 'MessageId');
// scripts/net/MessageId.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageId = void 0;
//消息 id
var MessageId;
(function (MessageId) {
    MessageId["MsgTypeCreateWs"] = "CreateWs";
    MessageId["MsgTypeNoticeUserCoin"] = "NoticeUserCoin";
    MessageId["MsgTypeHeartbeat"] = "Heartbeat";
    MessageId["MsgTypeLogin"] = "Login";
    MessageId["MsgTypeUserInfo"] = "UserInfo";
    MessageId["MsgTypePairRequest"] = "PairRequest";
    MessageId["MsgTypeCancelPair"] = "CancelPair";
    MessageId["MsgTypePairResult"] = "PairResult";
    MessageId["MsgTypeEnterRoom"] = "EnterRoom";
    MessageId["MsgTypeSitDown"] = "SitDown";
    MessageId["MsgTypeRobotSitDown"] = "RobotSitDown";
    MessageId["MsgTypeStand"] = "Stand";
    MessageId["MsgTypeReady"] = "Ready";
    MessageId["MsgTypeLeaveRoom"] = "LeaveRoom";
    MessageId["MsgTypeUserOffline"] = "UserOffline";
    MessageId["MsgTypeKickOutUser"] = "KickOutUser";
    MessageId["MsgTypeCreateInvite"] = "CreateInvite";
    MessageId["MsgTypeAcceptInvite"] = "AcceptInvite";
    MessageId["MsgTypeInviteReady"] = "InviteReady";
    MessageId["MsgTypeChgInviteCfg"] = "ChgInviteCfg";
    MessageId["MsgTypeLeaveInvite"] = "LeaveInvite";
    MessageId["MsgTypeNoticeInviteStatus"] = "NoticeInviteStatus";
    MessageId["MsgTypeInviteKickOut"] = "InviteKickOut";
    MessageId["MsgTypeInviteStart"] = "InviteStart";
    MessageId["MsgTypeViewerList"] = "ViewerList";
    MessageId["MsgTypeGameStart"] = "GameStart";
    MessageId["MsgTypeFirstMove"] = "FirstMove";
    MessageId["MsgTypeFirstMoveEnd"] = "FirstMoveEnd";
    MessageId["MsgTypeUserPosList"] = "UserPosList";
    MessageId["MsgTypeRollDice"] = "RollDice";
    MessageId["MsgTypeMoveChess"] = "MoveChess";
    MessageId["MsgTypeUseProp"] = "UseProp";
    MessageId["MsgTypeChoiceProp"] = "ChoiceProp";
    MessageId["MsgTypeChoicePropResult"] = "ChoicePropResult";
    MessageId["MsgTypeChoiceAdvance"] = "ChoiceAdvance";
    MessageId["MsgTypeMoveChessEnd"] = "MoveChessEnd";
    MessageId["MsgTypeSettlement"] = "Settlement";
    MessageId["MsgTypeProductConfigs"] = "ProductConfigs";
    MessageId["MsgTypeBuyProduct"] = "BuyProduct";
    MessageId["MsgTypeSetSkin"] = "SetSkin";
    MessageId["MsgTypeMoveBlock"] = "MoveBlock";
    MessageId["MsgTypeScoreChg"] = "ScoreChg";
    // 扫雷游戏相关消息
    MessageId["MsgTypeNoticeRoundStart"] = "NoticeRoundStart";
    MessageId["MsgTypeNoticeActionDisplay"] = "NoticeActionDisplay";
    MessageId["MsgTypeNoticeRoundEnd"] = "NoticeRoundEnd";
    MessageId["MsgTypeNoticeFirstChoiceBonus"] = "NoticeFirstChoiceBonus";
    MessageId["MsgTypeNoticeGameEnd"] = "NoticeGameEnd";
    MessageId["MsgTypeClickBlock"] = "ClickBlock";
    // 关卡进度相关消息
    MessageId["MsgTypeExtendLevelProgress"] = "ExtendLevelProgress";
    MessageId["MsgTypeExtendLevelInfo"] = "ExtendLevelInfo";
    MessageId["MsgTypeLevelClickBlock"] = "LevelClickBlock";
    MessageId["MsgTypeLevelGameEnd"] = "LevelGameEnd";
    MessageId["MsgTypeEndLevelGame"] = "EndLevelGame";
    // AI托管相关消息
    MessageId["MsgTypeAIStatusChange"] = "AIStatusChange";
    MessageId["MsgTypeCancelAIManagement"] = "CancelAIManagement";
})(MessageId = exports.MessageId || (exports.MessageId = {}));

cc._RF.pop();