{"version": 3, "sources": ["assets/scripts/game/AIManagedDialogController.ts"], "names": [], "mappings": ";;;;;AAAA,aAAa;AACb,kCAAkC;;;;;;;;;;;;;;;;;;;;;AAElC,4DAA2D;AAC3D,8CAA6C;AAC7C,yCAAwC;AAElC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAuD,6CAAY;IAAnE;QAAA,qEAkJC;QA/IG,aAAO,GAAY,IAAI,CAAC,CAAC,MAAM;QAG/B,cAAQ,GAAY,IAAI,CAAC,CAAC,gBAAgB;QAIlC,eAAS,GAAY,KAAK,CAAC,CAAC,SAAS;;IAwIjD,CAAC;IAtIG,yCAAK,GAAL;QACI,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEzB,gBAAgB;QAChB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;SACzE;IACL,CAAC;IAED;;OAEG;IACH,wCAAI,GAAJ;QACI,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,OAAO;SACV;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAIxB,UAAU;QACV,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;SAC5B;QAED,SAAS;QACT,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,wCAAI,GAAJ;QAAA,iBAWC;QAVG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO;SACV;QAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,SAAS;QACT,IAAI,CAAC,iBAAiB,CAAC;YACnB,KAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAC7B,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,qDAAiB,GAAzB;QACI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO;SACV;QAED,WAAW;QACX,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE;YACxB,KAAK,EAAE,CAAC;YACR,OAAO,EAAE,GAAG;SACf,EAAE;YACC,MAAM,EAAE,SAAS;SACpB,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;;OAGG;IACK,qDAAiB,GAAzB,UAA0B,QAAqB;QAC3C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,IAAI,QAAQ;gBAAE,QAAQ,EAAE,CAAC;YACzB,OAAO;SACV;QAED,WAAW;QACX,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;aACjB,EAAE,CAAC,eAAM,CAAC,eAAe,EAAE;YACxB,KAAK,EAAE,CAAC;YACR,OAAO,EAAE,CAAC;SACb,EAAE;YACC,MAAM,EAAE,QAAQ;SACnB,CAAC;aACD,IAAI,CAAC;YACF,IAAI,QAAQ;gBAAE,QAAQ,EAAE,CAAC;QAC7B,CAAC,CAAC;aACD,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,+CAAW,GAAnB;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO;SACV;QAED,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAE/B,aAAa;QACb,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,mBAAmB;QACnB,IAAI,CAAC,IAAI,EAAE,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,0DAAsB,GAA9B;QACI,IAAM,UAAU,GAAG;QACf,eAAe;SAClB,CAAC;QAEF,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,yBAAyB,EAAE,UAAU,CAAC,CAAC;QACxF,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,6CAAS,GAAT;QACI,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;IAC9C,CAAC;IAED,6CAAS,GAAT;QACI,SAAS;QACT,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;SAC1E;IACL,CAAC;IA9ID;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACM;IAGxB;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;+DACO;IANR,yBAAyB;QAD7C,OAAO;OACa,yBAAyB,CAkJ7C;IAAD,gCAAC;CAlJD,AAkJC,CAlJsD,EAAE,CAAC,SAAS,GAkJlE;kBAlJoB,yBAAyB", "file": "", "sourceRoot": "/", "sourcesContent": ["// AI托管中页面控制器\n// 当玩家进入AI托管状态时显示，点击屏幕任何位置发送取消托管消息\n\nimport { WebSocketManager } from \"../net/WebSocketManager\";\nimport { MessageId } from \"../net/MessageId\";\nimport { Config } from \"../util/Config\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class AIManagedDialogController extends cc.Component {\n\n    @property(cc.Node)\n    boardBg: cc.Node = null; // 背景板\n\n    @property(cc.Node)\n    maskNode: cc.Node = null; // 遮罩节点，用于接收点击事件\n\n   \n\n    private isShowing: boolean = false; // 是否正在显示\n\n    start() {\n        // 初始化时隐藏\n        this.node.active = false;\n        \n        // 为遮罩节点添加点击事件监听\n        if (this.maskNode) {\n            this.maskNode.on(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);\n        }\n    }\n\n    /**\n     * 显示托管中页面\n     */\n    show() {\n        if (this.isShowing) {\n            return;\n        }\n\n        this.isShowing = true;\n        this.node.active = true;\n\n       \n\n        // 初始化动画状态\n        if (this.boardBg) {\n            this.boardBg.scale = 0;\n            this.boardBg.opacity = 0;\n        }\n\n        // 执行显示动画\n        this.playShowAnimation();\n    }\n\n    /**\n     * 隐藏托管中页面\n     */\n    hide() {\n        if (!this.isShowing) {\n            return;\n        }\n\n        this.isShowing = false;\n\n        // 执行隐藏动画\n        this.playHideAnimation(() => {\n            this.node.active = false;\n        });\n    }\n\n    /**\n     * 播放显示动画\n     */\n    private playShowAnimation() {\n        if (!this.boardBg) {\n            return;\n        }\n\n        // 缩放和透明度动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { \n                scale: 1, \n                opacity: 255 \n            }, { \n                easing: 'backOut' \n            })\n            .start();\n    }\n\n    /**\n     * 播放隐藏动画\n     * @param callback 动画完成回调\n     */\n    private playHideAnimation(callback?: () => void) {\n        if (!this.boardBg) {\n            if (callback) callback();\n            return;\n        }\n\n        // 缩放和透明度动画\n        cc.tween(this.boardBg)\n            .to(Config.dialogScaleTime, { \n                scale: 0, \n                opacity: 0 \n            }, { \n                easing: 'backIn' \n            })\n            .call(() => {\n                if (callback) callback();\n            })\n            .start();\n    }\n\n    /**\n     * 遮罩点击事件处理\n     */\n    private onMaskClick() {\n        if (!this.isShowing) {\n            return;\n        }\n\n        console.log(\"点击屏幕，发送取消AI托管消息\");\n        \n        // 发送取消AI托管消息\n        this.sendCancelAIManagement();\n        \n        // 立即隐藏页面（不等待服务器响应）\n        this.hide();\n    }\n\n    /**\n     * 发送取消AI托管消息\n     */\n    private sendCancelAIManagement() {\n        const cancelData = {\n            // 可以根据需要添加其他参数\n        };\n\n        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeCancelAIManagement, cancelData);\n        console.log(\"已发送取消AI托管消息\");\n    }\n\n    /**\n     * 检查是否正在显示\n     */\n    isVisible(): boolean {\n        return this.isShowing && this.node.active;\n    }\n\n    onDestroy() {\n        // 清理事件监听\n        if (this.maskNode) {\n            this.maskNode.off(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);\n        }\n    }\n}\n"]}